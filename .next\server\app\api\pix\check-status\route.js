"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/pix/check-status/route";
exports.ids = ["app/api/pix/check-status/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fcheck-status%2Froute&page=%2Fapi%2Fpix%2Fcheck-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fcheck-status%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fcheck-status%2Froute&page=%2Fapi%2Fpix%2Fcheck-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fcheck-status%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_pix_check_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/pix/check-status/route.ts */ \"(rsc)/./app/api/pix/check-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/pix/check-status/route\",\n        pathname: \"/api/pix/check-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/pix/check-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\pix\\\\check-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_pix_check_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/pix/check-status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fcheck-status%2Froute&page=%2Fapi%2Fpix%2Fcheck-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fcheck-status%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/pix/check-status/route.ts":
/*!*******************************************!*\
  !*** ./app/api/pix/check-status/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n\n\nconst dynamic = \"force-dynamic\";\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const transaction_id = searchParams.get(\"transaction_id\");\n        console.log(\"\\uD83D\\uDD0D Verificando status PIX para transaction_id:\", transaction_id);\n        if (!transaction_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"transaction_id \\xe9 obrigat\\xf3rio\"\n            }, {\n                status: 400\n            });\n        }\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n        // Buscar bilhete no banco de dados\n        const bilhetes = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n      SELECT \n        id, codigo, transaction_id, status, valor_total, \n        created_at, updated_at, pix_order_id\n      FROM bilhetes \n      WHERE transaction_id = ? OR codigo = ?\n      LIMIT 1\n    `, [\n            transaction_id,\n            transaction_id\n        ]);\n        if (bilhetes.length === 0) {\n            console.log(\"❌ Bilhete n\\xe3o encontrado para transaction_id:\", transaction_id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Bilhete n\\xe3o encontrado\",\n                status: \"not_found\"\n            }, {\n                status: 404\n            });\n        }\n        const bilhete = bilhetes[0];\n        console.log(\"\\uD83D\\uDCCB Bilhete encontrado:\", {\n            id: bilhete.id,\n            codigo: bilhete.codigo,\n            status: bilhete.status,\n            valor: bilhete.valor_total\n        });\n        // Verificação com API real do PIX\n        let statusAtualizado = bilhete.status;\n        let shouldUpdate = false;\n        let providerResponse = null;\n        try {\n            // Integração com API meiodepagamento.com\n            console.log(\"\\uD83D\\uDD0D Consultando API PIX para transaction_id:\", bilhete.transaction_id);\n            const pixApiUrl = process.env.PIX_API_URL || \"https://api.meiodepagamento.com/api/V1\";\n            const pixApiToken = process.env.PIX_API_TOKEN || \"\";\n            if (bilhete.transaction_id && pixApiToken) {\n                // Consultar status do pagamento na API\n                const statusResponse = await fetch(`${pixApiUrl}/pix/status/${bilhete.transaction_id}`, {\n                    method: \"GET\",\n                    headers: {\n                        \"Authorization\": `Bearer ${pixApiToken}`,\n                        \"Content-Type\": \"application/json\",\n                        \"Accept\": \"application/json\"\n                    }\n                });\n                if (statusResponse.ok) {\n                    providerResponse = await statusResponse.json();\n                    console.log(\"\\uD83D\\uDCB3 Resposta da API PIX:\", providerResponse);\n                    // Verificar se o pagamento foi aprovado\n                    if (providerResponse.status === \"PAID\" || providerResponse.status === \"paid\" || providerResponse.status === \"approved\" || providerResponse.status === \"APPROVED\" || providerResponse.payment_status === \"PAID\") {\n                        console.log(\"✅ Pagamento aprovado via API PIX!\");\n                        statusAtualizado = \"pago\";\n                        shouldUpdate = true;\n                    } else if (providerResponse.status === \"PENDING\" || providerResponse.status === \"pending\") {\n                        console.log(\"⏳ Pagamento ainda pendente na API PIX\");\n                    } else if (providerResponse.status === \"CANCELLED\" || providerResponse.status === \"cancelled\" || providerResponse.status === \"EXPIRED\" || providerResponse.status === \"expired\") {\n                        console.log(\"❌ Pagamento cancelado/expirado na API PIX\");\n                        statusAtualizado = \"cancelado\";\n                        shouldUpdate = true;\n                    }\n                } else {\n                    console.log(\"⚠️ Erro na resposta da API PIX:\", statusResponse.status, statusResponse.statusText);\n                    // API não respondeu - manter status pendente\n                    console.log(\"⚠️ API PIX indispon\\xedvel - mantendo status pendente\");\n                    console.log(\"\\uD83D\\uDD12 FALLBACK AUTOM\\xc1TICO DESABILITADO - Aguardando webhook oficial\");\n                }\n            } else {\n                console.log(\"⚠️ Token da API PIX n\\xe3o configurado ou transaction_id inv\\xe1lido\");\n                console.log(\"\\uD83D\\uDD12 SIMULA\\xc7\\xc3O AUTOM\\xc1TICA DESABILITADA - Aguardando webhook oficial\");\n            }\n        } catch (providerError) {\n            console.log(\"⚠️ Erro ao consultar API PIX:\", providerError);\n            console.log(\"\\uD83D\\uDD12 FALLBACK POR ERRO DESABILITADO - Aguardando webhook oficial\");\n        }\n        // Se o status mudou, atualizar no banco\n        if (shouldUpdate) {\n            console.log(\"\\uD83D\\uDCBE Atualizando status do bilhete para:\", statusAtualizado);\n            await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        UPDATE bilhetes \n        SET status = ?, updated_at = NOW() \n        WHERE id = ?\n      `, [\n                statusAtualizado,\n                bilhete.id\n            ]);\n            // Disparar evento personalizado para notificar o frontend\n            console.log(\"\\uD83D\\uDCE1 Status atualizado com sucesso\");\n        }\n        const agora = new Date();\n        const criadoEm = new Date(bilhete.created_at);\n        const tempoDecorrido = (agora.getTime() - criadoEm.getTime()) / 1000;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            transaction_id: transaction_id,\n            bilhete_id: bilhete.id,\n            codigo: bilhete.codigo,\n            status: statusAtualizado,\n            status_anterior: bilhete.status,\n            valor: bilhete.valor_total,\n            updated: shouldUpdate,\n            tempo_decorrido: Math.round(tempoDecorrido),\n            provider_response: providerResponse,\n            message: shouldUpdate ? \"Status atualizado automaticamente\" : \"Status inalterado\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"❌ Erro ao verificar status PIX:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erro interno do servidor\",\n            message: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/pix/check-status/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fcheck-status%2Froute&page=%2Fapi%2Fpix%2Fcheck-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fcheck-status%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();