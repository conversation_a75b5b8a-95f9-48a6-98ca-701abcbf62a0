'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Wallet, 
  Plus, 
  CreditCard, 
  History, 
  DollarSign,
  ArrowUpCircle,
  ArrowDownCircle,
  ShoppingCart,
  Gift,
  X,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Transacao {
  id: number
  tipo: string
  valor: number
  saldoAnterior: number
  saldoPosterior: number
  descricao: string
  data: string
}

interface WalletModalProps {
  isOpen: boolean
  onClose: () => void
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
}

export function WalletModal({ isOpen, onClose, usuario: usuarioProp, onSaldoUpdate }: WalletModalProps) {
  const [usuario, setUsuario] = useState<Usuario>(usuarioProp || {
    id: 585,
    nome: 'G<PERSON>her<PERSON>',
    email: '<EMAIL>',
    saldo: 0.00
  })
  
  const [transacoes, setTransacoes] = useState<Transacao[]>([])
  const [loading, setLoading] = useState(false)
  const [valorDeposito, setValorDeposito] = useState('')
  const [valorBilhete, setValorBilhete] = useState('')
  const [qrCodePix, setQrCodePix] = useState<string | null>(null)
  const [depositoLoading, setDepositoLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'deposito' | 'historico'>('deposito')

  useEffect(() => {
    if (isOpen && usuarioProp) {
      setUsuario(usuarioProp)
      carregarDados()
    }
  }, [isOpen, usuarioProp])

  const carregarDados = async () => {
    try {
      setLoading(true)
      
      // Se for modo demo, não fazer requisições reais
      if (usuario.id === 585) {
        setLoading(false)
        return
      }

      // Carregar saldo e transações reais
      const balanceResponse = await fetch(`/api/wallet/balance?user_id=${usuario.id}`)
      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json()
        if (balanceData.success) {
          setUsuario(balanceData.usuario)
          setTransacoes(balanceData.transacoes)
          onSaldoUpdate?.(balanceData.usuario.saldo)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados da carteira')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const adicionarTransacao = (tipo: string, valor: number, descricao: string) => {
    const novaTransacao: Transacao = {
      id: Date.now(),
      tipo,
      valor,
      saldoAnterior: usuario.saldo,
      saldoPosterior: tipo === 'compra_bilhete' ? usuario.saldo - valor : usuario.saldo + valor,
      descricao,
      data: new Date().toLocaleString('pt-BR')
    }

    setTransacoes(prev => [novaTransacao, ...prev])
    
    // Atualizar saldo do usuário
    const novoSaldo = novaTransacao.saldoPosterior
    setUsuario(prev => ({
      ...prev,
      saldo: novoSaldo
    }))

    // Notificar componente pai sobre mudança de saldo
    onSaldoUpdate?.(novoSaldo)

    return novaTransacao
  }

  const criarDeposito = async () => {
    if (!valorDeposito || parseFloat(valorDeposito) <= 0) {
      toast.error('Digite um valor válido para depósito')
      return
    }

    try {
      setDepositoLoading(true)

      // Se for modo demo, simular
      if (usuario.id === 585) {
        setTimeout(() => {
          const qrCodeBase64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNvZGlnbyBQSVg8L3RleHQ+PC9zdmc+"
          setQrCodePix(qrCodeBase64)
          toast.success('QR Code PIX gerado! Clique em "Confirmar Pagamento" para simular')
          setDepositoLoading(false)
        }, 1000)
        return
      }

      // Criar depósito real
      const response = await fetch('/api/wallet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: usuario.id,
          valor: parseFloat(valorDeposito),
          client_name: usuario.nome,
          client_email: usuario.email,
          client_document: '12345678901'
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Depósito PIX criado com sucesso!')
        setQrCodePix(data.deposito.qrcode_image)
        carregarDados()
      } else {
        toast.error(data.error || 'Erro ao criar depósito')
      }

    } catch (error) {
      console.error('Erro ao criar depósito:', error)
      toast.error('Erro ao criar depósito PIX')
    } finally {
      setDepositoLoading(false)
    }
  }

  const confirmarPagamento = () => {
    if (!valorDeposito) return

    const valor = parseFloat(valorDeposito)
    adicionarTransacao('deposito', valor, `Depósito PIX - ${usuario.nome}`)
    
    toast.success(`Depósito de ${formatCurrency(valor)} confirmado!`)
    setValorDeposito('')
    setQrCodePix(null)
  }

  const comprarBilhete = () => {
    if (!valorBilhete || parseFloat(valorBilhete) <= 0) {
      toast.error('Digite um valor válido para o bilhete')
      return
    }

    const valor = parseFloat(valorBilhete)
    
    if (usuario.saldo < valor) {
      toast.error(`Saldo insuficiente! Saldo atual: ${formatCurrency(usuario.saldo)}`)
      return
    }

    adicionarTransacao('compra_bilhete', valor, `Compra de bilhete - BLT${Date.now()}`)
    toast.success(`Bilhete de ${formatCurrency(valor)} comprado com sucesso!`)
    setValorBilhete('')
  }

  const adicionarBonus = () => {
    const valor = 25.00
    adicionarTransacao('bonus', valor, 'Bônus de boas-vindas')
    toast.success(`Bônus de ${formatCurrency(valor)} adicionado!`)
  }

  const getTransactionIcon = (tipo: string) => {
    switch (tipo) {
      case 'deposito':
        return <ArrowUpCircle className="h-4 w-4 text-green-600" />
      case 'compra_bilhete':
        return <ArrowDownCircle className="h-4 w-4 text-red-600" />
      case 'bonus':
        return <Gift className="h-4 w-4 text-yellow-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header do Modal */}
        <div className="flex items-center justify-between p-6 border-b bg-slate-800 text-white">
          <div className="flex items-center space-x-3">
            <Wallet className="h-6 w-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-bold">Carteira Digital</h2>
              <p className="text-sm text-gray-300">{usuario.nome} - Saldo: {formatCurrency(usuario.saldo)}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-slate-700"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('deposito')}
            className={`flex-1 px-6 py-3 text-sm font-medium ${
              activeTab === 'deposito'
                ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Plus className="h-4 w-4 inline mr-2" />
            Adicionar Saldo
          </button>
          <button
            onClick={() => setActiveTab('historico')}
            className={`flex-1 px-6 py-3 text-sm font-medium ${
              activeTab === 'historico'
                ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <History className="h-4 w-4 inline mr-2" />
            Histórico
          </button>
        </div>

        {/* Conteúdo do Modal */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {activeTab === 'deposito' && (
            <div className="space-y-6">
              {/* Ações Rápidas */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <Input
                        type="number"
                        placeholder="Valor do bilhete (R$)"
                        value={valorBilhete}
                        onChange={(e) => setValorBilhete(e.target.value)}
                        min="0.01"
                        step="0.01"
                      />
                      <Button 
                        onClick={comprarBilhete}
                        className="flex items-center space-x-2"
                      >
                        <ShoppingCart className="h-4 w-4" />
                        <span>Comprar</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4 text-center">
                    <Button 
                      onClick={adicionarBonus}
                      className="w-full flex items-center justify-center space-x-2 bg-yellow-600 hover:bg-yellow-700"
                    >
                      <Gift className="h-4 w-4" />
                      <span>Bônus R$ 25,00</span>
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Saldo Atual</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {formatCurrency(usuario.saldo)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Depósito PIX */}
              <Card className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-green-600" />
                    Depósito via PIX
                  </CardTitle>
                  <CardDescription>
                    Adicione saldo à sua carteira através de PIX
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex space-x-4">
                    <Input
                      type="number"
                      placeholder="Valor do depósito (R$)"
                      value={valorDeposito}
                      onChange={(e) => setValorDeposito(e.target.value)}
                      min="0.01"
                      step="0.01"
                    />
                    <Button 
                      onClick={criarDeposito}
                      disabled={depositoLoading}
                      className="flex items-center space-x-2"
                    >
                      <CreditCard className="h-4 w-4" />
                      <span>{depositoLoading ? 'Gerando...' : 'Gerar PIX'}</span>
                    </Button>
                  </div>

                  {qrCodePix && (
                    <div className="mt-4 p-4 bg-white rounded-lg text-center border">
                      <p className="text-sm text-gray-600 mb-2">QR Code PIX gerado:</p>
                      <div className="w-32 h-32 mx-auto mb-4 border rounded">
                        <img 
                          src={qrCodePix} 
                          alt="QR Code PIX" 
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <Button 
                        onClick={confirmarPagamento}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Simular Pagamento Confirmado
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'historico' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Histórico de Transações</h3>
              
              {transacoes.length === 0 ? (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Nenhuma transação ainda. Faça um depósito ou adicione um bônus para começar!
                  </p>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {transacoes.map((transacao) => (
                    <div key={transacao.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transacao.tipo)}
                        <div>
                          <p className="font-medium text-gray-900">{transacao.descricao}</p>
                          <p className="text-sm text-gray-500">{transacao.data}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${
                          transacao.tipo === 'compra_bilhete' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transacao.tipo === 'compra_bilhete' ? '-' : '+'}
                          {formatCurrency(transacao.valor)}
                        </p>
                        <p className="text-xs text-gray-500">
                          Saldo: {formatCurrency(transacao.saldoPosterior)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
