"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/boloes/route";
exports.ids = ["app/api/boloes/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2Froute&page=%2Fapi%2Fboloes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2Froute&page=%2Fapi%2Fboloes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_boloes_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/boloes/route.ts */ \"(rsc)/./app/api/boloes/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/boloes/route\",\n        pathname: \"/api/boloes\",\n        filename: \"route\",\n        bundlePath: \"app/api/boloes/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\boloes\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_boloes_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/boloes/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2Froute&page=%2Fapi%2Fboloes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/boloes/route.ts":
/*!*********************************!*\
  !*** ./app/api/boloes/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n\n\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDD04 Buscando bol\\xf5es reais...\");\n        // Tentar buscar bolões reais, mas com fallback para dados de teste\n        let boloes = [];\n        try {\n            boloes = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        SELECT * FROM boloes\n        WHERE status IN ('ativo', 'em_breve')\n        ORDER BY id DESC\n        LIMIT 10\n      `);\n            console.log(`📊 Bolões encontrados: ${boloes.length}`);\n        } catch (dbError) {\n            console.warn(\"⚠️ Erro ao conectar com banco, usando dados de teste:\", dbError);\n            // Retornar dados de teste imediatamente se houver erro de conexão\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                boloes: [\n                    {\n                        id: 999,\n                        nome: \"Bol\\xe3o Brasil - Teste\",\n                        descricao: \"Bol\\xe3o de teste com times brasileiros reais\",\n                        valor_aposta: 25.0,\n                        premio_total: 1000.0,\n                        data_inicio: new Date().toISOString(),\n                        data_fim: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n                        status: \"ativo\",\n                        participantes: 0,\n                        max_participantes: 100,\n                        total_jogos: 11,\n                        criador: \"Admin\",\n                        banner_image: \"/placeholder.svg?height=200&width=400\",\n                        campeonatos_selecionados: [\n                            {\n                                codigo: \"BSA\",\n                                nome: \"Campeonato Brasileiro S\\xe9rie A\"\n                            }\n                        ],\n                        jogos: [\n                            {\n                                id: 2001,\n                                time_casa: \"Flamengo\",\n                                time_fora: \"Palmeiras\",\n                                time_casa_logo: \"https://crests.football-data.org/1783.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1769.png\",\n                                data_jogo: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2002,\n                                time_casa: \"Corinthians\",\n                                time_fora: \"S\\xe3o Paulo\",\n                                time_casa_logo: \"https://crests.football-data.org/1779.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1776.png\",\n                                data_jogo: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2003,\n                                time_casa: \"Santos\",\n                                time_fora: \"Botafogo\",\n                                time_casa_logo: \"https://crests.football-data.org/6685.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1770.png\",\n                                data_jogo: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2004,\n                                time_casa: \"Vasco da Gama\",\n                                time_fora: \"Fluminense\",\n                                time_casa_logo: \"https://crests.football-data.org/1780.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1765.png\",\n                                data_jogo: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2005,\n                                time_casa: \"Gr\\xeamio\",\n                                time_fora: \"Internacional\",\n                                time_casa_logo: \"https://crests.football-data.org/1767.png\",\n                                time_fora_logo: \"https://crests.football-data.org/6684.png\",\n                                data_jogo: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2006,\n                                time_casa: \"Atl\\xe9tico Mineiro\",\n                                time_fora: \"Cruzeiro\",\n                                time_casa_logo: \"https://crests.football-data.org/1766.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1771.png\",\n                                data_jogo: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2007,\n                                time_casa: \"Bahia\",\n                                time_fora: \"Vit\\xf3ria\",\n                                time_casa_logo: \"https://crests.football-data.org/1777.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1782.png\",\n                                data_jogo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2008,\n                                time_casa: \"Fortaleza\",\n                                time_fora: \"Cear\\xe1\",\n                                time_casa_logo: \"https://crests.football-data.org/3984.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1837.png\",\n                                data_jogo: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2009,\n                                time_casa: \"Bragantino\",\n                                time_fora: \"Juventude\",\n                                time_casa_logo: \"https://crests.football-data.org/4286.png\",\n                                time_fora_logo: \"https://crests.football-data.org/4245_large.png\",\n                                data_jogo: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2010,\n                                time_casa: \"Sport Recife\",\n                                time_fora: \"Mirassol\",\n                                time_casa_logo: \"https://crests.football-data.org/1778.png\",\n                                time_fora_logo: \"https://crests.football-data.org/4364.png\",\n                                data_jogo: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            },\n                            {\n                                id: 2011,\n                                time_casa: \"Flamengo\",\n                                time_fora: \"Vasco da Gama\",\n                                time_casa_logo: \"https://crests.football-data.org/1783.png\",\n                                time_fora_logo: \"https://crests.football-data.org/1780.png\",\n                                data_jogo: new Date(Date.now() + 11 * 24 * 60 * 60 * 1000).toISOString(),\n                                campeonato: \"Campeonato Brasileiro S\\xe9rie A\",\n                                campeonato_codigo: \"BSA\",\n                                status: \"agendado\"\n                            }\n                        ]\n                    }\n                ]\n            });\n        }\n        // Formatar bolões e buscar jogos\n        const boloesFormatados = await Promise.all(boloes.map(async (bolao)=>{\n            let campeonatos = [];\n            let jogos = [];\n            // Tentar parsear campeonatos selecionados\n            if (bolao.campeonatos_selecionados) {\n                try {\n                    campeonatos = JSON.parse(bolao.campeonatos_selecionados);\n                } catch (e) {\n                    console.warn(`⚠️ Erro ao parsear campeonatos do bolão ${bolao.id}`);\n                }\n            }\n            // Buscar jogos do bolão\n            try {\n                // Primeiro tentar buscar da tabela bolao_jogos\n                const jogosAssociados = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n          SELECT\n            j.*,\n            tc.nome as time_casa_nome,\n            tc.nome_curto as time_casa_curto,\n            tc.logo_url as time_casa_logo,\n            tf.nome as time_fora_nome,\n            tf.nome_curto as time_fora_curto,\n            tf.logo_url as time_fora_logo,\n            c.nome as campeonato_nome,\n            c.codigo as campeonato_codigo\n          FROM bolao_jogos bj\n          JOIN jogos j ON bj.jogo_id = j.id\n          LEFT JOIN times tc ON j.time_casa_id = tc.id\n          LEFT JOIN times tf ON j.time_fora_id = tf.id\n          LEFT JOIN campeonatos c ON j.campeonato_id = c.id\n          WHERE bj.bolao_id = ?\n          ORDER BY j.data_jogo ASC\n        `, [\n                    bolao.id\n                ]);\n                // Se não há jogos associados, tentar buscar das partidas selecionadas\n                if (jogosAssociados.length === 0 && bolao.partidas_selecionadas) {\n                    try {\n                        const partidasSelecionadas = JSON.parse(bolao.partidas_selecionadas);\n                        if (partidasSelecionadas && partidasSelecionadas.length > 0) {\n                            console.log(`🔍 Buscando ${partidasSelecionadas.length} partidas selecionadas para bolão ${bolao.id}`);\n                            // Buscar jogos das partidas selecionadas\n                            const partidasIds = partidasSelecionadas.map((p)=>p.id || p).filter(Boolean);\n                            if (partidasIds.length > 0) {\n                                const placeholders = partidasIds.map(()=>\"?\").join(\",\");\n                                const jogosPartidas = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n                  SELECT\n                    j.*,\n                    tc.nome as time_casa_nome,\n                    tc.nome_curto as time_casa_curto,\n                    tc.logo_url as time_casa_logo,\n                    tf.nome as time_fora_nome,\n                    tf.nome_curto as time_fora_curto,\n                    tf.logo_url as time_fora_logo,\n                    c.nome as campeonato_nome,\n                    c.codigo as campeonato_codigo\n                  FROM jogos j\n                  LEFT JOIN times tc ON j.time_casa_id = tc.id\n                  LEFT JOIN times tf ON j.time_fora_id = tf.id\n                  LEFT JOIN campeonatos c ON j.campeonato_id = c.id\n                  WHERE j.id IN (${placeholders})\n                  ORDER BY j.data_jogo ASC\n                `, partidasIds);\n                                jogos = jogosPartidas;\n                            }\n                        }\n                    } catch (e) {\n                        console.warn(`⚠️ Erro ao parsear partidas selecionadas do bolão ${bolao.id}`);\n                    }\n                } else if (jogosAssociados.length === 0 && campeonatos.length > 0) {\n                    console.log(`🔍 Buscando jogos de TODOS os campeonatos selecionados para bolão ${bolao.id}`);\n                    // Buscar jogos de TODOS os campeonatos selecionados (MÁXIMO 11 TOTAL)\n                    const codigosCampeonatos = campeonatos.map((c)=>c.codigo).filter(Boolean);\n                    if (codigosCampeonatos.length > 0) {\n                        console.log(`🏆 Campeonatos: ${codigosCampeonatos.join(\", \")}`);\n                        // Calcular quantos jogos por campeonato (distribuição equilibrada)\n                        const jogosPorCampeonato = Math.ceil(11 / codigosCampeonatos.length);\n                        console.log(`📊 Distribuição: ${jogosPorCampeonato} jogos por campeonato (máx 11 total)`);\n                        // Buscar jogos de cada campeonato separadamente para garantir representação de todos\n                        let todosJogos = [];\n                        for (const codigo of codigosCampeonatos){\n                            const jogosCampeonato = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n                SELECT\n                  j.*,\n                  tc.nome as time_casa_nome,\n                  tc.nome_curto as time_casa_curto,\n                  tc.logo_url as time_casa_logo,\n                  tf.nome as time_fora_nome,\n                  tf.nome_curto as time_fora_curto,\n                  tf.logo_url as time_fora_logo,\n                  c.nome as campeonato_nome,\n                  c.codigo as campeonato_codigo\n                FROM jogos j\n                LEFT JOIN times tc ON j.time_casa_id = tc.id\n                LEFT JOIN times tf ON j.time_fora_id = tf.id\n                LEFT JOIN campeonatos c ON j.campeonato_id = c.id\n                WHERE c.codigo = ?\n                AND j.status IN ('agendado', 'ao_vivo')\n                AND j.data_jogo >= NOW()\n                ORDER BY j.data_jogo ASC\n                LIMIT ?\n              `, [\n                                codigo,\n                                jogosPorCampeonato\n                            ]);\n                            console.log(`📊 ${codigo}: ${jogosCampeonato.length} jogos encontrados`);\n                            todosJogos = [\n                                ...todosJogos,\n                                ...jogosCampeonato\n                            ];\n                        }\n                        // Ordenar todos os jogos por data\n                        jogos = todosJogos.sort((a, b)=>new Date(a.data_jogo).getTime() - new Date(b.data_jogo).getTime());\n                        // Se não temos 11 jogos, buscar mais de qualquer campeonato\n                        if (jogos.length < 11) {\n                            console.log(`⚠️ Apenas ${jogos.length} jogos encontrados, buscando mais...`);\n                            const jogosAdicionais = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n                SELECT\n                  j.*,\n                  tc.nome as time_casa_nome,\n                  tc.nome_curto as time_casa_curto,\n                  tc.logo_url as time_casa_logo,\n                  tf.nome as time_fora_nome,\n                  tf.nome_curto as time_fora_curto,\n                  tf.logo_url as time_fora_logo,\n                  c.nome as campeonato_nome,\n                  c.codigo as campeonato_codigo\n                FROM jogos j\n                LEFT JOIN times tc ON j.time_casa_id = tc.id\n                LEFT JOIN times tf ON j.time_fora_id = tf.id\n                LEFT JOIN campeonatos c ON j.campeonato_id = c.id\n                WHERE j.status IN ('agendado', 'ao_vivo')\n                AND j.data_jogo >= NOW()\n                ORDER BY j.data_jogo ASC\n                LIMIT ?\n              `, [\n                                11\n                            ]);\n                            // Combinar e remover duplicatas\n                            const jogosExistentesIds = new Set(jogos.map((j)=>j.id));\n                            const novosjogos = jogosAdicionais.filter((j)=>!jogosExistentesIds.has(j.id));\n                            jogos = [\n                                ...jogos,\n                                ...novosjogos\n                            ].sort((a, b)=>new Date(a.data_jogo).getTime() - new Date(b.data_jogo).getTime()).slice(0, 11);\n                            console.log(`✅ Total final de jogos: ${jogos.length}`);\n                        } else {\n                            // Limitar a 11 se temos mais\n                            jogos = jogos.slice(0, 11);\n                            console.log(`✅ Total de jogos carregados: ${jogos.length} (limitado a 11)`);\n                        }\n                    }\n                } else {\n                    jogos = jogosAssociados;\n                }\n            } catch (error) {\n                console.error(`❌ Erro ao buscar jogos do bolão ${bolao.id}:`, error);\n            }\n            return {\n                id: bolao.id,\n                nome: bolao.nome || \"Bol\\xe3o\",\n                descricao: bolao.descricao || \"Descri\\xe7\\xe3o do bol\\xe3o\",\n                valor_aposta: parseFloat(bolao.valor_aposta || 25),\n                premio_total: parseFloat(bolao.premio_total || 1000),\n                data_inicio: bolao.data_inicio,\n                data_fim: bolao.data_fim,\n                status: bolao.status,\n                participantes: 0,\n                max_participantes: bolao.max_participantes || 100,\n                total_jogos: jogos.length,\n                criador: \"Admin\",\n                banner_image: bolao.banner_image,\n                campeonatos_selecionados: campeonatos,\n                jogos: jogos.map((jogo)=>{\n                    // Debug: Log dos dados do jogo\n                    console.log(`🔍 Jogo ${jogo.id}:`, {\n                        time_casa_nome: jogo.time_casa_nome,\n                        time_casa: jogo.time_casa,\n                        time_fora_nome: jogo.time_fora_nome,\n                        time_fora: jogo.time_fora,\n                        campeonato_nome: jogo.campeonato_nome,\n                        campeonato_codigo: jogo.campeonato_codigo\n                    });\n                    return {\n                        id: jogo.id,\n                        time_casa: jogo.time_casa_nome || jogo.time_casa || \"Time Casa\",\n                        time_fora: jogo.time_fora_nome || jogo.time_fora || \"Time Fora\",\n                        time_casa_logo: jogo.time_casa_logo || \"/placeholder.svg\",\n                        time_fora_logo: jogo.time_fora_logo || \"/placeholder.svg\",\n                        data_jogo: jogo.data_jogo,\n                        campeonato: jogo.campeonato_nome || \"Campeonato\",\n                        campeonato_codigo: jogo.campeonato_codigo || \"CAMP\",\n                        status: jogo.status,\n                        resultado_casa: jogo.resultado_casa,\n                        resultado_fora: jogo.resultado_fora\n                    };\n                })\n            };\n        }));\n        console.log(`✅ Retornando ${boloesFormatados.length} bolões reais`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            boloes: boloesFormatados\n        });\n    } catch (error) {\n        console.error(\"❌ Erro ao buscar bol\\xf5es:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erro interno do servidor\",\n            message: error instanceof Error ? error.message : \"Erro desconhecido\",\n            boloes: []\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { nome, descricao, valor_aposta, premio_total, max_participantes, min_acertos, data_inicio, data_fim, jogos_selecionados, regras } = body;\n        if (!nome || !valor_aposta || !premio_total || !data_inicio || !data_fim) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Dados obrigat\\xf3rios n\\xe3o fornecidos\"\n            }, {\n                status: 400\n            });\n        }\n        // Por enquanto, vamos usar um usuário padrão como criador\n        // Em produção, isso deveria vir da sessão do usuário\n        const criado_por = 1;\n        // Inserir bolão\n        const result = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n      INSERT INTO boloes (\n        nome, descricao, valor_aposta, premio_total, max_participantes,\n        min_acertos, data_inicio, data_fim, status, criado_por, regras\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'em_breve', ?, ?)\n    `, [\n            nome,\n            descricao,\n            valor_aposta,\n            premio_total,\n            max_participantes || null,\n            min_acertos || 3,\n            data_inicio,\n            data_fim,\n            criado_por,\n            JSON.stringify(regras || [])\n        ]);\n        const bolao_id = result.insertId;\n        // Inserir jogos do bolão se fornecidos\n        if (jogos_selecionados && jogos_selecionados.length > 0) {\n            for (const jogo_id of jogos_selecionados){\n                await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n          INSERT INTO bolao_jogos (bolao_id, jogo_id)\n          VALUES (?, ?)\n        `, [\n                    bolao_id,\n                    jogo_id\n                ]);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            bolao_id,\n            message: \"Bol\\xe3o criado com sucesso\"\n        });\n    } catch (error) {\n        console.error(\"❌ Erro ao criar bol\\xe3o:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erro interno do servidor\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/boloes/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2Froute&page=%2Fapi%2Fboloes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();