"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/usuarios/route";
exports.ids = ["app/api/admin/usuarios/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusuarios%2Froute&page=%2Fapi%2Fadmin%2Fusuarios%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusuarios%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusuarios%2Froute&page=%2Fapi%2Fadmin%2Fusuarios%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusuarios%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_admin_usuarios_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/usuarios/route.ts */ \"(rsc)/./app/api/admin/usuarios/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/usuarios/route\",\n        pathname: \"/api/admin/usuarios\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/usuarios/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\admin\\\\usuarios\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_admin_usuarios_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/usuarios/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusuarios%2Froute&page=%2Fapi%2Fadmin%2Fusuarios%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusuarios%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/usuarios/route.ts":
/*!*****************************************!*\
  !*** ./app/api/admin/usuarios/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n\n\n// Força renderização dinâmica para evitar erro de build estático\nconst dynamic = \"force-dynamic\";\nasync function GET(request) {\n    try {\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n        const { searchParams } = new URL(request.url);\n        const filters = {\n            status: searchParams.get(\"status\") || undefined,\n            tipo: searchParams.get(\"tipo\") || undefined,\n            search: searchParams.get(\"search\") || undefined\n        };\n        const [usuarios, stats] = await Promise.all([\n            (0,_lib_database__WEBPACK_IMPORTED_MODULE_2__.getUsuarios)(filters),\n            (0,_lib_database__WEBPACK_IMPORTED_MODULE_2__.getUsuariosStats)()\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            usuarios: usuarios || [],\n            stats: stats || {\n                total: 0,\n                ativos: 0,\n                cambistas: 0,\n                bloqueados: 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Erro ao buscar usu\\xe1rios:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erro interno do servidor\",\n            usuarios: [],\n            stats: {\n                total: 0,\n                ativos: 0,\n                cambistas: 0,\n                bloqueados: 0\n            }\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n        const body = await request.json();\n        const { id, nome, email, telefone, cpf, tipo, status, senha } = body;\n        console.log(\"✏️ Atualizando usu\\xe1rio:\", {\n            id,\n            nome,\n            email,\n            tipo,\n            status\n        });\n        // Validações básicas\n        if (!id || !nome || !email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"ID, nome e email s\\xe3o obrigat\\xf3rios\"\n            }, {\n                status: 400\n            });\n        }\n        // Verificar se usuário existe\n        const existingUser = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuerySingle)(\"SELECT id FROM usuarios WHERE id = ?\", [\n            id\n        ]);\n        if (!existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Usu\\xe1rio n\\xe3o encontrado\"\n            }, {\n                status: 404\n            });\n        }\n        // Verificar se email já existe em outro usuário\n        const emailExists = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuerySingle)(\"SELECT id FROM usuarios WHERE email = ? AND id != ?\", [\n            email,\n            id\n        ]);\n        if (emailExists) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Email j\\xe1 est\\xe1 em uso por outro usu\\xe1rio\"\n            }, {\n                status: 409\n            });\n        }\n        // Preparar parâmetros seguros (apenas campos que existem na tabela)\n        const safeParams = {\n            nome: nome || null,\n            email: email || null,\n            tipo: tipo || \"usuario\",\n            status: status || \"ativo\"\n        };\n        console.log(\"\\uD83D\\uDD0D Par\\xe2metros seguros:\", safeParams);\n        // Preparar query de atualização (sem campos que não existem)\n        let updateQuery = `\n      UPDATE usuarios SET\n        nome = ?,\n        email = ?,\n        tipo = ?,\n        status = ?,\n        data_atualizacao = NOW()\n      WHERE id = ?\n    `;\n        let updateParams = [\n            safeParams.nome,\n            safeParams.email,\n            safeParams.tipo,\n            safeParams.status,\n            id\n        ];\n        // Se senha foi fornecida, incluir na atualização\n        if (senha && senha.trim() !== \"\") {\n            const senhaHash = await bcryptjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"].hash(senha, 10);\n            updateQuery = `\n        UPDATE usuarios SET\n          nome = ?,\n          email = ?,\n          tipo = ?,\n          status = ?,\n          senha_hash = ?,\n          data_atualizacao = NOW()\n        WHERE id = ?\n      `;\n            updateParams = [\n                safeParams.nome,\n                safeParams.email,\n                safeParams.tipo,\n                safeParams.status,\n                senhaHash,\n                id\n            ];\n        }\n        console.log(\"\\uD83D\\uDD0D Query:\", updateQuery);\n        console.log(\"\\uD83D\\uDD0D Par\\xe2metros:\", updateParams);\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(updateQuery, updateParams);\n        console.log(\"✅ Usu\\xe1rio atualizado com sucesso:\", id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Usu\\xe1rio atualizado com sucesso\"\n        });\n    } catch (error) {\n        console.error(\"❌ Erro ao atualizar usu\\xe1rio:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erro interno do servidor\",\n            message: \"N\\xe3o foi poss\\xedvel atualizar o usu\\xe1rio\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get(\"id\");\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"ID do usu\\xe1rio \\xe9 obrigat\\xf3rio\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"\\uD83D\\uDDD1️ Deletando usu\\xe1rio:\", id);\n        // Verificar se usuário existe\n        const existingUser = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuerySingle)(\"SELECT id FROM usuarios WHERE id = ?\", [\n            id\n        ]);\n        if (!existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Usu\\xe1rio n\\xe3o encontrado\"\n            }, {\n                status: 404\n            });\n        }\n        // Deletar usuário (as indicações serão mantidas com foreign key null)\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(\"DELETE FROM usuarios WHERE id = ?\", [\n            id\n        ]);\n        console.log(\"✅ Usu\\xe1rio deletado com sucesso:\", id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Usu\\xe1rio deletado com sucesso\"\n        });\n    } catch (error) {\n        console.error(\"❌ Erro ao deletar usu\\xe1rio:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erro interno do servidor\",\n            message: \"N\\xe3o foi poss\\xedvel deletar o usu\\xe1rio\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/usuarios/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDatabaseSetup: () => (/* binding */ checkDatabaseSetup),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createAfiliado: () => (/* binding */ createAfiliado),\n/* harmony export */   createBolao: () => (/* binding */ createBolao),\n/* harmony export */   createCambista: () => (/* binding */ createCambista),\n/* harmony export */   deleteAfiliado: () => (/* binding */ deleteAfiliado),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getAfiliados: () => (/* binding */ getAfiliados),\n/* harmony export */   getAfiliadosStats: () => (/* binding */ getAfiliadosStats),\n/* harmony export */   getBoloes: () => (/* binding */ getBoloes),\n/* harmony export */   getBoloesStats: () => (/* binding */ getBoloesStats),\n/* harmony export */   getCambistas: () => (/* binding */ getCambistas),\n/* harmony export */   getCambistasStats: () => (/* binding */ getCambistasStats),\n/* harmony export */   getCampeonatos: () => (/* binding */ getCampeonatos),\n/* harmony export */   getCampeonatosStats: () => (/* binding */ getCampeonatosStats),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getJogos: () => (/* binding */ getJogos),\n/* harmony export */   getJogosStats: () => (/* binding */ getJogosStats),\n/* harmony export */   getUsuarios: () => (/* binding */ getUsuarios),\n/* harmony export */   getUsuariosStats: () => (/* binding */ getUsuariosStats),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   updateAfiliado: () => (/* binding */ updateAfiliado)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n\nlet db = null;\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: process.env.DB_PORT || 3306,\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com tratamento de erro\nasync function executeQuery(query, params = []) {\n    try {\n        // Validar parâmetros para evitar undefined\n        const safeParams = params.map((param)=>{\n            if (param === undefined) {\n                console.warn(\"⚠️ Par\\xe2metro undefined detectado, convertendo para null\");\n                return null;\n            }\n            return param;\n        });\n        // Log para debug\n        if (safeParams.some((p)=>p === null && params.some((p)=>p === undefined))) {\n            console.log(\"\\uD83D\\uDD0D Par\\xe2metros originais:\", params);\n            console.log(\"\\uD83D\\uDD0D Par\\xe2metros seguros:\", safeParams);\n        }\n        const connection = await getDatabase();\n        const [results] = await connection.execute(query, safeParams);\n        return results;\n    } catch (error) {\n        console.error(\"❌ Erro ao executar query:\", error);\n        console.error(\"Query:\", query);\n        console.error(\"Params originais:\", params);\n        console.error(\"Params processados:\", params.map((p)=>p === undefined ? \"UNDEFINED\" : p));\n        throw error;\n    }\n}\n// Função para executar queries que retornam uma única linha\nasync function executeQuerySingle(query, params = []) {\n    try {\n        const results = await executeQuery(query, params);\n        return results.length > 0 ? results[0] : null;\n    } catch (error) {\n        console.error(\"❌ Erro ao executar query single:\", error);\n        throw error;\n    }\n}\n// Função para verificar se o banco de dados está configurado\nasync function checkDatabaseSetup() {\n    try {\n        const connection = await getDatabase();\n        // Verificar se as tabelas principais existem\n        const [tables] = await connection.execute(`\n      SELECT TABLE_NAME\n      FROM INFORMATION_SCHEMA.TABLES\n      WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('usuarios', 'campeonatos', 'boloes', 'apostas', 'bolao_jogos')\n    `, [\n            process.env.DB_NAME || \"sistema-bolao-top\"\n        ]);\n        const requiredTables = [\n            \"usuarios\",\n            \"campeonatos\",\n            \"boloes\",\n            \"apostas\",\n            \"bolao_jogos\"\n        ];\n        const existingTables = tables.map((t)=>t.TABLE_NAME);\n        const missingTables = requiredTables.filter((table)=>!existingTables.includes(table));\n        if (missingTables.length > 0) {\n            console.warn(\"⚠️ Tabelas faltando no banco de dados:\", missingTables);\n            // Criar tabela bolao_jogos se não existir\n            if (missingTables.includes(\"bolao_jogos\")) {\n                try {\n                    await connection.execute(`\n            CREATE TABLE IF NOT EXISTS bolao_jogos (\n              id INT AUTO_INCREMENT PRIMARY KEY,\n              bolao_id INT NOT NULL,\n              jogo_id INT NOT NULL,\n              data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n              INDEX idx_bolao_id (bolao_id),\n              INDEX idx_jogo_id (jogo_id),\n              UNIQUE KEY unique_bolao_jogo (bolao_id, jogo_id)\n            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n          `);\n                    console.log(\"✅ Tabela bolao_jogos criada com sucesso\");\n                } catch (error) {\n                    console.error(\"❌ Erro ao criar tabela bolao_jogos:\", error);\n                }\n            }\n            console.warn(\"Execute o script scripts/mysql-setup.sql para criar as demais tabelas\");\n            return false;\n        }\n        console.log(\"✅ Todas as tabelas necess\\xe1rias est\\xe3o presentes\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ Erro ao verificar configura\\xe7\\xe3o do banco:\", error);\n        return false;\n    }\n}\n// ==================== FUNÇÕES PARA USUÁRIOS ====================\nasync function getUsuarios(filters = {}) {\n    try {\n        let query = \"SELECT * FROM usuarios WHERE 1=1\";\n        const params = [];\n        if (filters.status && filters.status !== \"todos\") {\n            query += \" AND status = ?\";\n            params.push(filters.status);\n        }\n        if (filters.tipo && filters.tipo !== \"todos\") {\n            query += \" AND tipo = ?\";\n            params.push(filters.tipo);\n        }\n        if (filters.search) {\n            query += \" AND (nome LIKE ? OR email LIKE ?)\";\n            params.push(`%${filters.search}%`, `%${filters.search}%`);\n        }\n        query += \" ORDER BY data_cadastro DESC\";\n        const usuarios = await executeQuery(query, params);\n        return usuarios || [];\n    } catch (error) {\n        console.error(\"Erro ao buscar usu\\xe1rios:\", error);\n        return [];\n    }\n}\nasync function getUsuariosStats() {\n    try {\n        const [total, ativos, cambistas, bloqueados] = await Promise.all([\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM usuarios\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE tipo = \"cambista\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE status = \"bloqueado\"').catch(()=>({\n                    count: 0\n                }))\n        ]);\n        return {\n            total: total?.count || 0,\n            ativos: ativos?.count || 0,\n            cambistas: cambistas?.count || 0,\n            bloqueados: bloqueados?.count || 0\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats de usu\\xe1rios:\", error);\n        return {\n            total: 0,\n            ativos: 0,\n            cambistas: 0,\n            bloqueados: 0\n        };\n    }\n}\n// ==================== FUNÇÕES PARA CAMBISTAS ====================\nasync function getCambistas(filters = {}) {\n    try {\n        let query = 'SELECT * FROM usuarios WHERE tipo = \"cambista\"';\n        const params = [];\n        if (filters.search) {\n            query += \" AND (nome LIKE ? OR email LIKE ?)\";\n            params.push(`%${filters.search}%`, `%${filters.search}%`);\n        }\n        query += \" ORDER BY data_cadastro DESC\";\n        const cambistas = await executeQuery(query, params);\n        return cambistas || [];\n    } catch (error) {\n        console.error(\"Erro ao buscar cambistas:\", error);\n        return [];\n    }\n}\n// ==================== FUNÇÕES PARA AFILIADOS ====================\nasync function getAfiliados(filters = {}) {\n    try {\n        let query = `\n      SELECT\n        a.*,\n        u.nome as nome_usuario,\n        u.email as email_usuario,\n        u.status as status_usuario\n      FROM afiliados a\n      LEFT JOIN usuarios u ON a.usuario_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (filters.status && filters.status !== \"todos\") {\n            query += \" AND a.status = ?\";\n            params.push(filters.status);\n        }\n        if (filters.search) {\n            query += \" AND (a.nome LIKE ? OR a.email LIKE ? OR a.codigo_afiliado LIKE ?)\";\n            params.push(`%${filters.search}%`, `%${filters.search}%`, `%${filters.search}%`);\n        }\n        query += \" ORDER BY a.data_cadastro DESC\";\n        const afiliados = await executeQuery(query, params);\n        return afiliados || [];\n    } catch (error) {\n        console.error(\"Erro ao buscar afiliados:\", error);\n        return [];\n    }\n}\nasync function getAfiliadosStats() {\n    try {\n        const [total, ativos, inativos, comissoes] = await Promise.all([\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM afiliados\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM afiliados WHERE status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM afiliados WHERE status = \"inativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(`\n        SELECT\n          COALESCE(SUM(comissao_total), 0) as total_comissoes,\n          COALESCE(SUM(CASE WHEN DATE(data_cadastro) = CURDATE() THEN comissao_total ELSE 0 END), 0) as comissoes_hoje\n        FROM afiliados\n      `).catch(()=>({\n                    total_comissoes: 0,\n                    comissoes_hoje: 0\n                }))\n        ]);\n        return {\n            total: total?.count || 0,\n            ativos: ativos?.count || 0,\n            inativos: inativos?.count || 0,\n            totalComissoes: comissoes?.total_comissoes || 0,\n            comissoesHoje: comissoes?.comissoes_hoje || 0\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats de afiliados:\", error);\n        return {\n            total: 0,\n            ativos: 0,\n            inativos: 0,\n            totalComissoes: 0,\n            comissoesHoje: 0\n        };\n    }\n}\nasync function createAfiliado(data) {\n    try {\n        // Verificar se o email já existe\n        const existingAfiliado = await executeQuerySingle(\"SELECT id FROM afiliados WHERE email = ?\", [\n            data.email\n        ]);\n        if (existingAfiliado) {\n            throw new Error(\"Email j\\xe1 cadastrado como afiliado\");\n        }\n        // Gerar código único do afiliado\n        const codigoAfiliado = `AF${Date.now().toString().slice(-6)}`;\n        // Hash da senha (em produção, use bcrypt)\n        const senhaHash = `$2b$10$${data.senha}_hash_exemplo`;\n        const result = await executeQuery(`INSERT INTO afiliados (nome, email, telefone, codigo_afiliado, percentual_comissao, cpa_valor, tipo_comissao, senha_hash, status)\n       VALUES (?, ?, ?, ?, ?, ?, ?, ?, \"ativo\")`, [\n            data.nome,\n            data.email,\n            data.telefone,\n            codigoAfiliado,\n            data.percentual_comissao || 5,\n            data.cpa_valor || 0,\n            data.tipo_comissao || \"percentual\",\n            senhaHash\n        ]);\n        return result.insertId;\n    } catch (error) {\n        console.error(\"Erro ao criar afiliado:\", error);\n        throw error;\n    }\n}\nasync function updateAfiliado(id, data) {\n    try {\n        const updates = [];\n        const params = [];\n        if (data.nome) {\n            updates.push(\"nome = ?\");\n            params.push(data.nome);\n        }\n        if (data.email) {\n            updates.push(\"email = ?\");\n            params.push(data.email);\n        }\n        if (data.telefone) {\n            updates.push(\"telefone = ?\");\n            params.push(data.telefone);\n        }\n        if (data.percentual_comissao !== undefined) {\n            updates.push(\"percentual_comissao = ?\");\n            params.push(data.percentual_comissao);\n        }\n        if (data.status) {\n            updates.push(\"status = ?\");\n            params.push(data.status);\n        }\n        if (data.senha) {\n            const senhaHash = `$2b$10$${data.senha}_hash_exemplo`;\n            updates.push(\"senha_hash = ?\");\n            params.push(senhaHash);\n        }\n        if (updates.length === 0) {\n            throw new Error(\"Nenhum campo para atualizar\");\n        }\n        updates.push(\"data_atualizacao = NOW()\");\n        params.push(id);\n        const query = `UPDATE afiliados SET ${updates.join(\", \")} WHERE id = ?`;\n        await executeQuery(query, params);\n        return true;\n    } catch (error) {\n        console.error(\"Erro ao atualizar afiliado:\", error);\n        throw error;\n    }\n}\nasync function deleteAfiliado(id) {\n    try {\n        await executeQuery(\"DELETE FROM afiliados WHERE id = ?\", [\n            id\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Erro ao deletar afiliado:\", error);\n        throw error;\n    }\n}\nasync function createCambista(data) {\n    try {\n        // Verificar se o email já existe\n        const existingUser = await executeQuerySingle(\"SELECT id FROM usuarios WHERE email = ?\", [\n            data.email\n        ]);\n        if (existingUser) {\n            throw new Error(\"Email j\\xe1 cadastrado no sistema\");\n        }\n        // Hash da senha (em produção, use bcrypt)\n        const senhaHash = `$2b$10$${data.senha}_hash_exemplo`;\n        const result = await executeQuery(`INSERT INTO usuarios (nome, email, telefone, endereco, cpf_cnpj, senha_hash, tipo, status)\n       VALUES (?, ?, ?, ?, ?, ?, \"cambista\", \"ativo\")`, [\n            data.nome,\n            data.email,\n            data.telefone,\n            data.endereco || null,\n            data.cpf_cnpj || null,\n            senhaHash\n        ]);\n        return result.insertId;\n    } catch (error) {\n        console.error(\"Erro ao criar cambista:\", error);\n        throw error;\n    }\n}\nasync function getCambistasStats() {\n    try {\n        const [ativos, vendas] = await Promise.all([\n            executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE tipo = \"cambista\" AND status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(`\n        SELECT\n          COALESCE(SUM(a.valor_total), 0) as total_vendas,\n          COALESCE(SUM(CASE WHEN DATE(a.data_aposta) = CURDATE() THEN a.valor_total ELSE 0 END), 0) as vendas_hoje\n        FROM apostas a\n        JOIN usuarios u ON a.usuario_id = u.id\n        WHERE u.tipo = 'cambista'\n      `).catch(()=>({\n                    total_vendas: 0,\n                    vendas_hoje: 0\n                }))\n        ]);\n        const totalVendas = vendas?.total_vendas || 0;\n        const vendasHoje = vendas?.vendas_hoje || 0;\n        return {\n            ativos: ativos?.count || 0,\n            totalVendas: totalVendas,\n            vendasHoje: vendasHoje,\n            totalComissoes: totalVendas * 0.1\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats de cambistas:\", error);\n        return {\n            ativos: 0,\n            totalVendas: 0,\n            vendasHoje: 0,\n            totalComissoes: 0\n        };\n    }\n}\n// ==================== FUNÇÕES PARA CAMPEONATOS ====================\nasync function getCampeonatos() {\n    try {\n        const campeonatos = await executeQuery(\"SELECT * FROM campeonatos ORDER BY data_criacao DESC\");\n        return campeonatos || [];\n    } catch (error) {\n        console.error(\"Erro ao buscar campeonatos:\", error);\n        return [];\n    }\n}\nasync function getCampeonatosStats() {\n    try {\n        const [ativos, jogosHoje, total] = await Promise.all([\n            executeQuerySingle('SELECT COUNT(*) as count FROM campeonatos WHERE status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = CURDATE()\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM campeonatos\").catch(()=>({\n                    count: 0\n                }))\n        ]);\n        return {\n            ativos: ativos?.count || 0,\n            jogosHoje: jogosHoje?.count || 0,\n            internacionais: Math.floor((total?.count || 0) / 2)\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats de campeonatos:\", error);\n        return {\n            ativos: 0,\n            jogosHoje: 0,\n            internacionais: 0\n        };\n    }\n}\n// ==================== FUNÇÕES PARA BOLÕES ====================\nasync function getBoloes() {\n    try {\n        const boloes = await executeQuery(`\n      SELECT\n        b.*,\n        COALESCE(u.nome, 'Usuário não encontrado') as criado_por_nome\n      FROM boloes b\n      LEFT JOIN usuarios u ON b.criado_por = u.id\n      ORDER BY b.data_criacao DESC\n    `);\n        return boloes || [];\n    } catch (error) {\n        console.error(\"Erro ao buscar bol\\xf5es:\", error);\n        return [];\n    }\n}\nasync function getBoloesStats() {\n    try {\n        const [ativos, participantes, faturamento, finalizandoHoje] = await Promise.all([\n            executeQuerySingle('SELECT COUNT(*) as count FROM boloes WHERE status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(DISTINCT usuario_id) as count FROM apostas\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = \"paga\"').catch(()=>({\n                    total: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM boloes WHERE DATE(data_fim) = CURDATE()\").catch(()=>({\n                    count: 0\n                }))\n        ]);\n        return {\n            ativos: ativos?.count || 0,\n            participantes: participantes?.count || 0,\n            faturamento: faturamento?.total || 0,\n            finalizandoHoje: finalizandoHoje?.count || 0\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats de bol\\xf5es:\", error);\n        return {\n            ativos: 0,\n            participantes: 0,\n            faturamento: 0,\n            finalizandoHoje: 0\n        };\n    }\n}\nasync function createBolao(bolaoData) {\n    try {\n        console.log(\"\\uD83D\\uDD0D Dados recebidos para criar bol\\xe3o:\", bolaoData);\n        const { nome, descricao, valor_aposta, premio_total, max_participantes, min_acertos, data_inicio, data_fim, status, criado_por, regras, campeonatos_selecionados, partidas_selecionadas } = bolaoData;\n        // Preparar os parâmetros para a query\n        const params = [\n            nome,\n            descricao,\n            valor_aposta,\n            premio_total,\n            max_participantes || 100,\n            min_acertos || 3,\n            data_inicio,\n            data_fim,\n            status || \"ativo\",\n            criado_por,\n            regras ? JSON.stringify(regras) : null,\n            campeonatos_selecionados ? JSON.stringify(campeonatos_selecionados) : null,\n            partidas_selecionadas ? JSON.stringify(partidas_selecionadas) : null\n        ];\n        console.log(\"\\uD83D\\uDD0D Par\\xe2metros da query:\", params);\n        console.log(\"\\uD83D\\uDD0D N\\xfamero de par\\xe2metros:\", params.length);\n        const result = await executeQuery(`\n      INSERT INTO boloes (\n        nome, descricao, valor_aposta, premio_total, max_participantes,\n        min_acertos, data_inicio, data_fim, status, criado_por, regras,\n        campeonatos_selecionados, partidas_selecionadas\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `, params);\n        const bolaoId = result.insertId;\n        console.log(\"✅ Bol\\xe3o criado com ID:\", bolaoId);\n        // Associar partidas ao bolão na tabela bolao_jogos\n        if (partidas_selecionadas && Array.isArray(partidas_selecionadas) && partidas_selecionadas.length > 0) {\n            console.log(\"\\uD83D\\uDD0D Associando partidas ao bol\\xe3o:\", partidas_selecionadas);\n            for (const partida of partidas_selecionadas){\n                // Verificar se partida é um objeto ou apenas um ID\n                const partidaId = typeof partida === \"object\" ? partida.id : partida;\n                if (partidaId) {\n                    // Verificar se o jogo existe antes de tentar associar\n                    const jogoExiste = await executeQuerySingle(\"SELECT id FROM jogos WHERE id = ?\", [\n                        partidaId\n                    ]);\n                    if (jogoExiste) {\n                        console.log(\"\\uD83D\\uDD0D Inserindo partida ID:\", partidaId);\n                        await executeQuery(`\n              INSERT INTO bolao_jogos (bolao_id, jogo_id) VALUES (?, ?)\n            `, [\n                            bolaoId,\n                            partidaId\n                        ]);\n                    } else {\n                        console.warn(\"⚠️ Jogo com ID\", partidaId, \"n\\xe3o encontrado na tabela jogos. Pulando...\");\n                    }\n                }\n            }\n        }\n        return bolaoId;\n    } catch (error) {\n        console.error(\"❌ Erro ao criar bol\\xe3o:\", error);\n        console.error(\"❌ Stack trace:\", error.stack);\n        throw error;\n    }\n}\n// ==================== FUNÇÕES PARA JOGOS ====================\nasync function getJogos(filters = {}) {\n    try {\n        let query = `\n      SELECT\n        j.*,\n        c.nome as campeonato_nome,\n        tc.nome as time_casa_nome,\n        tc.nome_curto as time_casa_curto,\n        tc.logo_url as time_casa_logo,\n        tf.nome as time_fora_nome,\n        tf.nome_curto as time_fora_curto,\n        tf.logo_url as time_fora_logo\n      FROM jogos j\n      LEFT JOIN campeonatos c ON j.campeonato_id = c.id\n      LEFT JOIN times tc ON j.time_casa_id = tc.id\n      LEFT JOIN times tf ON j.time_fora_id = tf.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (filters.campeonato_id) {\n            query += \" AND j.campeonato_id = ?\";\n            params.push(filters.campeonato_id);\n        }\n        if (filters.status) {\n            query += \" AND j.status = ?\";\n            params.push(filters.status);\n        }\n        if (filters.data_inicio && filters.data_fim) {\n            query += \" AND DATE(j.data_jogo) BETWEEN ? AND ?\";\n            params.push(filters.data_inicio, filters.data_fim);\n        } else if (filters.data_inicio) {\n            query += \" AND DATE(j.data_jogo) >= ?\";\n            params.push(filters.data_inicio);\n        } else if (filters.data_fim) {\n            query += \" AND DATE(j.data_jogo) <= ?\";\n            params.push(filters.data_fim);\n        }\n        query += \" ORDER BY j.data_jogo ASC\";\n        if (filters.limit) {\n            query += \" LIMIT ?\";\n            params.push(parseInt(filters.limit));\n        }\n        const jogos = await executeQuery(query, params);\n        return jogos || [];\n    } catch (error) {\n        console.error(\"Erro ao buscar jogos:\", error);\n        return [];\n    }\n}\nasync function getJogosStats() {\n    try {\n        const [hoje, semana, total, aoVivo] = await Promise.all([\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = CURDATE()\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM jogos WHERE data_jogo BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM jogos\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM jogos WHERE status = \"ao_vivo\"').catch(()=>({\n                    count: 0\n                }))\n        ]);\n        return {\n            hoje: hoje?.count || 0,\n            semana: semana?.count || 0,\n            total: total?.count || 0,\n            aoVivo: aoVivo?.count || 0\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats de jogos:\", error);\n        return {\n            hoje: 0,\n            semana: 0,\n            total: 0,\n            aoVivo: 0\n        };\n    }\n}\n// ==================== FUNÇÕES PARA DASHBOARD ====================\nasync function getDashboardStats() {\n    try {\n        const [boloes, usuarios, faturamento, apostas, cambistas, jogosHoje] = await Promise.all([\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM boloes\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COALESCE(SUM(valor_total), 0) as total FROM apostas WHERE status = \"paga\" AND MONTH(data_aposta) = MONTH(CURDATE())').catch(()=>({\n                    total: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM apostas WHERE DATE(data_aposta) = CURDATE()\").catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle('SELECT COUNT(*) as count FROM usuarios WHERE tipo = \"cambista\" AND status = \"ativo\"').catch(()=>({\n                    count: 0\n                })),\n            executeQuerySingle(\"SELECT COUNT(*) as count FROM jogos WHERE DATE(data_jogo) = CURDATE()\").catch(()=>({\n                    count: 0\n                }))\n        ]);\n        return {\n            totalBoloes: boloes?.count || 0,\n            totalUsuarios: usuarios?.count || 0,\n            faturamentoMes: faturamento?.total || 0,\n            apostasHoje: apostas?.count || 0,\n            cambistasAtivos: cambistas?.count || 0,\n            jogosHoje: jogosHoje?.count || 0\n        };\n    } catch (error) {\n        console.error(\"Erro ao buscar stats do dashboard:\", error);\n        return {\n            totalBoloes: 0,\n            totalUsuarios: 0,\n            faturamentoMes: 0,\n            apostasHoje: 0,\n            cambistasAtivos: 0,\n            jogosHoje: 0\n        };\n    }\n}\n// ==================== FUNÇÕES AUXILIARES ====================\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fusuarios%2Froute&page=%2Fapi%2Fadmin%2Fusuarios%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusuarios%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();