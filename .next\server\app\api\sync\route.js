"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sync/route";
exports.ids = ["app/api/sync/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync%2Froute&page=%2Fapi%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync%2Froute&page=%2Fapi%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/sync/route.ts */ \"(rsc)/./app/api/sync/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sync/route\",\n        pathname: \"/api/sync\",\n        filename: \"route\",\n        bundlePath: \"app/api/sync/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\sync\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/sync/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync%2Froute&page=%2Fapi%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sync/route.ts":
/*!*******************************!*\
  !*** ./app/api/sync/route.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const { action } = await request.json();\n        if (action === \"sync-now\") {\n            console.log(\"\\uD83D\\uDD04 Sincroniza\\xe7\\xe3o manual temporariamente desabilitada\");\n            // await syncFootballData() // Temporariamente desabilitado devido a erros de banco\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"Sincroniza\\xe7\\xe3o temporariamente desabilitada - usando dados locais\"\n            });\n        }\n        if (action === \"start-auto-sync\") {\n            console.log(\"⏰ Sincroniza\\xe7\\xe3o autom\\xe1tica temporariamente desabilitada\");\n            // startAutoSync() // Temporariamente desabilitado devido a erros de banco\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"Sincroniza\\xe7\\xe3o autom\\xe1tica temporariamente desabilitada\"\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"A\\xe7\\xe3o n\\xe3o reconhecida\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"❌ Erro na API de sincroniza\\xe7\\xe3o:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Erro interno do servidor\",\n            error: error instanceof Error ? error.message : \"Erro desconhecido\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"API de sincroniza\\xe7\\xe3o funcionando\",\n            endpoints: {\n                \"POST /api/sync\": {\n                    \"sync-now\": \"Executa sincroniza\\xe7\\xe3o imediatamente\",\n                    \"start-auto-sync\": \"Inicia sincroniza\\xe7\\xe3o autom\\xe1tica de hora em hora\"\n                }\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Erro ao verificar status da API\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sync/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsync%2Froute&page=%2Fapi%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();