'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON>etHeader, StatusBadge } from '@/components/WalletHeader'
import { 
  Wallet, 
  Plus, 
  CreditCard, 
  History, 
  DollarSign,
  ArrowUpCircle,
  ArrowDownCircle,
  ShoppingCart,
  Gift
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Transacao {
  id: number
  tipo: string
  valor: number
  saldoAnterior: number
  saldoPosterior: number
  descricao: string
  data: string
}

export default function WalletDemoPage() {
  const [usuario, setUsuario] = useState<Usuario>({
    id: 585,
    nome: 'Guilherme',
    email: '<EMAIL>',
    saldo: 0.00
  })
  
  const [transacoes, setTransacoes] = useState<Transacao[]>([])
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [valorDeposito, setValorDeposito] = useState('')
  const [valorBilhete, setValorBilhete] = useState('')
  const [qrCodePix, setQrCodePix] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const adicionarTransacao = (tipo: string, valor: number, descricao: string) => {
    const novaTransacao: Transacao = {
      id: Date.now(),
      tipo,
      valor,
      saldoAnterior: usuario.saldo,
      saldoPosterior: tipo === 'compra_bilhete' ? usuario.saldo - valor : usuario.saldo + valor,
      descricao,
      data: new Date().toLocaleString('pt-BR')
    }

    setTransacoes(prev => [novaTransacao, ...prev])
    
    // Atualizar saldo do usuário
    setUsuario(prev => ({
      ...prev,
      saldo: novaTransacao.saldoPosterior
    }))

    return novaTransacao
  }

  const simularDeposito = () => {
    if (!valorDeposito || parseFloat(valorDeposito) <= 0) {
      toast.error('Digite um valor válido para depósito')
      return
    }

    setLoading(true)

    // Simular geração de QR Code
    setTimeout(() => {
      const qrCodeBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
      setQrCodePix(qrCodeBase64)
      toast.success('QR Code PIX gerado! Simule o pagamento clicando em "Confirmar Pagamento"')
      setLoading(false)
    }, 1000)
  }

  const confirmarPagamento = () => {
    if (!valorDeposito) return

    const valor = parseFloat(valorDeposito)
    adicionarTransacao('deposito', valor, `Depósito PIX - ${usuario.nome}`)
    
    toast.success(`Depósito de ${formatCurrency(valor)} confirmado!`)
    setValorDeposito('')
    setQrCodePix(null)
    setShowDepositModal(false)
  }

  const simularCompraBilhete = () => {
    if (!valorBilhete || parseFloat(valorBilhete) <= 0) {
      toast.error('Digite um valor válido para o bilhete')
      return
    }

    const valor = parseFloat(valorBilhete)
    
    if (usuario.saldo < valor) {
      toast.error(`Saldo insuficiente! Saldo atual: ${formatCurrency(usuario.saldo)}`)
      return
    }

    adicionarTransacao('compra_bilhete', valor, `Compra de bilhete - BLT${Date.now()}`)
    toast.success(`Bilhete de ${formatCurrency(valor)} comprado com sucesso!`)
    setValorBilhete('')
  }

  const adicionarBonus = () => {
    const valor = 25.00
    adicionarTransacao('bonus', valor, 'Bônus de boas-vindas')
    toast.success(`Bônus de ${formatCurrency(valor)} adicionado!`)
  }

  const getTransactionIcon = (tipo: string) => {
    switch (tipo) {
      case 'deposito':
        return <ArrowUpCircle className="h-4 w-4 text-green-600" />
      case 'compra_bilhete':
        return <ArrowDownCircle className="h-4 w-4 text-red-600" />
      case 'bonus':
        return <Gift className="h-4 w-4 text-yellow-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header seguindo o padrão do sistema */}
      <WalletHeader 
        usuario={usuario}
        onDepositoClick={() => setShowDepositModal(true)}
        onSearchClick={() => toast.info('Função de busca em desenvolvimento')}
      />

      <div className="space-y-6 p-6">
        {/* Título da Página */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Wallet className="h-8 w-8 mr-3 text-blue-600" />
              Demo - Sistema de Carteira
            </h1>
            <p className="text-gray-600 mt-2">Demonstração do novo sistema de saldo</p>
          </div>
        </div>

        {/* Ações Rápidas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Input
                  type="number"
                  placeholder="Valor do bilhete (R$)"
                  value={valorBilhete}
                  onChange={(e) => setValorBilhete(e.target.value)}
                  min="0.01"
                  step="0.01"
                />
                <Button 
                  onClick={simularCompraBilhete}
                  className="flex items-center space-x-2"
                >
                  <ShoppingCart className="h-4 w-4" />
                  <span>Comprar</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Button 
                onClick={adicionarBonus}
                className="w-full flex items-center justify-center space-x-2 bg-yellow-600 hover:bg-yellow-700"
              >
                <Gift className="h-4 w-4" />
                <span>Bônus R$ 25,00</span>
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">Saldo Atual</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(usuario.saldo)}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Modal de Depósito */}
        {showDepositModal && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Plus className="h-5 w-5 mr-2 text-green-600" />
                  Adicionar Saldo via PIX
                </div>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    setShowDepositModal(false)
                    setQrCodePix(null)
                    setValorDeposito('')
                  }}
                >
                  ✕
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <Input
                  type="number"
                  placeholder="Valor do depósito (R$)"
                  value={valorDeposito}
                  onChange={(e) => setValorDeposito(e.target.value)}
                  min="0.01"
                  step="0.01"
                />
                <Button 
                  onClick={simularDeposito}
                  disabled={loading}
                  className="flex items-center space-x-2"
                >
                  <CreditCard className="h-4 w-4" />
                  <span>{loading ? 'Gerando...' : 'Gerar PIX'}</span>
                </Button>
              </div>

              {qrCodePix && (
                <div className="mt-4 p-4 bg-white rounded-lg text-center border">
                  <p className="text-sm text-gray-600 mb-2">QR Code PIX gerado:</p>
                  <div className="w-32 h-32 bg-gray-200 mx-auto mb-4 flex items-center justify-center">
                    <span className="text-xs text-gray-500">QR Code PIX</span>
                  </div>
                  <Button 
                    onClick={confirmarPagamento}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Simular Pagamento Confirmado
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Histórico de Transações */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <History className="h-5 w-5 mr-2 text-gray-600" />
              Histórico de Transações
            </CardTitle>
          </CardHeader>
          <CardContent>
            {transacoes.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                Nenhuma transação ainda. Faça um depósito ou adicione um bônus para começar!
              </p>
            ) : (
              <div className="space-y-3">
                {transacoes.map((transacao) => (
                  <div key={transacao.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getTransactionIcon(transacao.tipo)}
                      <div>
                        <p className="font-medium text-gray-900">{transacao.descricao}</p>
                        <p className="text-sm text-gray-500">{transacao.data}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${
                        transacao.tipo === 'compra_bilhete' ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {transacao.tipo === 'compra_bilhete' ? '-' : '+'}
                        {formatCurrency(transacao.valor)}
                      </p>
                      <p className="text-xs text-gray-500">
                        Saldo: {formatCurrency(transacao.saldoPosterior)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
