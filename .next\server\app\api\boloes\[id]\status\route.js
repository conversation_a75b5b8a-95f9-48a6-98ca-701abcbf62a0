"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/boloes/[id]/status/route";
exports.ids = ["app/api/boloes/[id]/status/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&page=%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&page=%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_boloes_id_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/boloes/[id]/status/route.ts */ \"(rsc)/./app/api/boloes/[id]/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/boloes/[id]/status/route\",\n        pathname: \"/api/boloes/[id]/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/boloes/[id]/status/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\boloes\\\\[id]\\\\status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_boloes_id_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/boloes/[id]/status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&page=%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/boloes/[id]/status/route.ts":
/*!*********************************************!*\
  !*** ./app/api/boloes/[id]/status/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n\n\nasync function GET(request, { params }) {\n    try {\n        console.log(`🔍 Buscando status do bolão ID: ${params.id}`);\n        const bolaoId = parseInt(params.id);\n        try {\n            await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n            // Buscar informações do bolão com timeout\n            const bolao = await Promise.race([\n                (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`SELECT * FROM boloes WHERE id = ?`, [\n                    bolaoId\n                ]),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout na consulta do bol\\xe3o\")), 3000))\n            ]);\n            if (!bolao || bolao.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: \"Bol\\xe3o n\\xe3o encontrado\"\n                }, {\n                    status: 404\n                });\n            }\n            const bolaoData = bolao[0];\n            // Buscar partidas do bolão\n            let partidas = [];\n            try {\n                const partidasSelecionadas = JSON.parse(bolaoData.partidas_selecionadas || \"[]\");\n                const partidasIds = partidasSelecionadas.map((p)=>p.id || p);\n                if (partidasIds.length > 0) {\n                    const placeholders = partidasIds.map(()=>\"?\").join(\",\");\n                    partidas = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n          SELECT \n            j.*,\n            tc.nome as time_casa_nome,\n            tf.nome as time_fora_nome\n          FROM jogos j\n          LEFT JOIN times tc ON j.time_casa_id = tc.id\n          LEFT JOIN times tf ON j.time_fora_id = tf.id\n          WHERE j.id IN (${placeholders})\n          ORDER BY j.data_jogo ASC\n        `, partidasIds);\n                }\n            } catch (error) {\n                console.error(\"Erro ao buscar partidas:\", error);\n            }\n            // Verificar se algum jogo já começou\n            const agora = new Date();\n            const jogosIniciados = partidas.filter((partida)=>{\n                const dataJogo = new Date(partida.data_jogo);\n                return dataJogo <= agora;\n            });\n            // Verificar se todos os jogos terminaram\n            const jogosFinalizados = partidas.filter((partida)=>{\n                return partida.status === \"finalizado\";\n            });\n            const apostasEncerradas = jogosIniciados.length > 0;\n            const bolaoFinalizado = jogosFinalizados.length === partidas.length && partidas.length > 0;\n            // Se o bolão finalizou, buscar ranking\n            let ranking = [];\n            if (bolaoFinalizado) {\n                ranking = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        SELECT \n          u.id,\n          u.nome,\n          COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) as acertos,\n          COUNT(ad.id) as total_apostas,\n          ROUND((COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) / COUNT(ad.id)) * 100, 2) as percentual_acertos\n        FROM usuarios u\n        JOIN apostas a ON u.id = a.usuario_id\n        JOIN aposta_detalhes ad ON a.id = ad.aposta_id\n        JOIN jogos j ON ad.jogo_id = j.id\n        WHERE a.bolao_id = ? AND a.status = 'paga'\n        GROUP BY u.id, u.nome\n        ORDER BY acertos DESC, percentual_acertos DESC\n        LIMIT 10\n      `, [\n                    bolaoId\n                ]);\n            }\n            console.log(`✅ Status do bolão ${bolaoId} carregado do banco`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                bolao: {\n                    id: bolaoData.id,\n                    nome: bolaoData.nome,\n                    status: bolaoData.status\n                },\n                apostas_encerradas: apostasEncerradas,\n                bolao_finalizado: bolaoFinalizado,\n                total_partidas: partidas.length,\n                jogos_iniciados: jogosIniciados.length,\n                jogos_finalizados: jogosFinalizados.length,\n                ranking: ranking,\n                proxima_partida: jogosIniciados.length === 0 && partidas.length > 0 ? partidas[0] : null,\n                source: \"database\"\n            });\n        } catch (dbError) {\n            console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, dbError.message);\n            // Retornar dados básicos sem erro\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                bolao: {\n                    id: bolaoId,\n                    nome: `Bolão ${bolaoId}`,\n                    status: \"ativo\"\n                },\n                apostas_encerradas: false,\n                bolao_finalizado: false,\n                total_partidas: 0,\n                jogos_iniciados: 0,\n                jogos_finalizados: 0,\n                ranking: [],\n                proxima_partida: null,\n                source: \"fallback\"\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Erro ao verificar status do bol\\xe3o:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erro interno do servidor\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/boloes/[id]/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&page=%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fboloes%2F%5Bid%5D%2Fstatus%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();