"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/styled-jsx";
exports.ids = ["vendor-chunks/styled-jsx"];
exports.modules = {

/***/ "(ssr)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n__webpack_require__(/*! client-only */ \"(ssr)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node =  false && 0;\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (false) {}\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (true) {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || \"undefined\" === \"undefined\") {\n            var sheet =  false ? 0 : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else { var tag; }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (true) {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (false) {} else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (true) {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (true) {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (false) {}\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry =  false ? 0 : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (true) {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/styled-jsx/dist/index/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/styled-jsx/style.js":
/*!******************************************!*\
  !*** ./node_modules/styled-jsx/style.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! ./dist/index */ \"(ssr)/./node_modules/styled-jsx/dist/index/index.js\").style;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3R5bGVkLWpzeC9zdHlsZS5qcyIsIm1hcHBpbmdzIjoiO0FBQUFBLHFIQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL3Npc3RlbWEtYm9sYW8vLi9ub2RlX21vZHVsZXMvc3R5bGVkLWpzeC9zdHlsZS5qcz8zNzBiIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2luZGV4Jykuc3R5bGVcbiJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSIsInN0eWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/styled-jsx/style.js\n");

/***/ })

};
;