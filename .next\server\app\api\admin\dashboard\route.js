"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/dashboard/route";
exports.ids = ["app/api/admin/dashboard/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_admin_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/dashboard/route.ts */ \"(rsc)/./app/api/admin/dashboard/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/dashboard/route\",\n        pathname: \"/api/admin/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/dashboard/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\admin\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_admin_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/dashboard/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/dashboard/route.ts":
/*!******************************************!*\
  !*** ./app/api/admin/dashboard/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n\n\nconst dynamic = \"force-dynamic\";\nasync function GET() {\n    try {\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n        console.log(\"\\uD83D\\uDCCA Coletando dados do dashboard administrativo...\");\n        // Estatísticas básicas com tratamento de erro individual\n        const stats = {};\n        try {\n            const [totalBoloes] = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`SELECT COUNT(*) as count FROM boloes`);\n            stats.totalBoloes = totalBoloes?.count || 0;\n        } catch (error) {\n            console.error(\"Erro ao buscar bol\\xf5es:\", error);\n            stats.totalBoloes = 0;\n        }\n        try {\n            const [totalUsuarios] = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`SELECT COUNT(*) as count FROM usuarios`);\n            stats.totalUsuarios = totalUsuarios?.count || 0;\n        } catch (error) {\n            console.error(\"Erro ao buscar usu\\xe1rios:\", error);\n            stats.totalUsuarios = 0;\n        }\n        try {\n            const [faturamento] = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        SELECT COALESCE(SUM(valor_total), 0) as total\n        FROM bilhetes\n        WHERE status = 'pago'\n        AND MONTH(created_at) = MONTH(NOW())\n        AND YEAR(created_at) = YEAR(NOW())\n      `);\n            stats.faturamentoMes = faturamento?.total || 0;\n        } catch (error) {\n            console.error(\"Erro ao buscar faturamento:\", error);\n            stats.faturamentoMes = 0;\n        }\n        try {\n            const [apostasHoje] = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        SELECT COUNT(*) as count\n        FROM bilhetes\n        WHERE DATE(created_at) = CURDATE()\n      `);\n            stats.apostasHoje = apostasHoje?.count || 0;\n        } catch (error) {\n            console.error(\"Erro ao buscar apostas hoje:\", error);\n            stats.apostasHoje = 0;\n        }\n        try {\n            const [cambistasAtivos] = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        SELECT COUNT(*) as count\n        FROM usuarios\n        WHERE tipo = 'cambista' AND status = 'ativo'\n      `);\n            stats.cambistasAtivos = cambistasAtivos?.count || 0;\n        } catch (error) {\n            console.error(\"Erro ao buscar cambistas:\", error);\n            stats.cambistasAtivos = 0;\n        }\n        try {\n            const [jogosHoje] = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        SELECT COUNT(*) as count\n        FROM jogos\n        WHERE DATE(data_jogo) = CURDATE()\n      `);\n            stats.jogosHoje = jogosHoje?.count || 0;\n        } catch (error) {\n            console.error(\"Erro ao buscar jogos hoje:\", error);\n            stats.jogosHoje = 0;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            stats\n        });\n    } catch (error) {\n        console.error(\"❌ Erro ao carregar dashboard:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erro interno do servidor\",\n            message: error?.message || \"Erro desconhecido\",\n            stats: {\n                totalBoloes: 0,\n                totalUsuarios: 0,\n                faturamentoMes: 0,\n                apostasHoje: 0,\n                cambistasAtivos: 0,\n                jogosHoje: 0\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();