/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Ccomponents%5C%5CAutoSyncProvider.tsx%22%2C%22ids%22%3A%5B%22AutoSyncProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Ccomponents%5C%5CAutoSyncProvider.tsx%22%2C%22ids%22%3A%5B%22AutoSyncProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/AutoSyncProvider.tsx */ \"(app-pages-browser)/./components/AutoSyncProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDeWFrdXMlNUMlNUNEZXNrdG9wJTVDJTVDeGFtcDguMSU1QyU1Q2h0ZG9jcyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3lha3VzJTVDJTVDRGVza3RvcCU1QyU1Q3hhbXA4LjElNUMlNUNodGRvY3MlNUMlNUNjb21wb25lbnRzJTVDJTVDQXV0b1N5bmNQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRvU3luY1Byb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3lha3VzJTVDJTVDRGVza3RvcCU1QyU1Q3hhbXA4LjElNUMlNUNodGRvY3MlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNzb25uZXIlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFpRztBQUNqRztBQUNBLDRMQUF1SjtBQUN2SjtBQUNBLGtNQUFtSiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2FmNmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx5YWt1c1xcXFxEZXNrdG9wXFxcXHhhbXA4LjFcXFxcaHRkb2NzXFxcXGFwcFxcXFxnbG9iYWxzLmNzc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0b1N5bmNQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHlha3VzXFxcXERlc2t0b3BcXFxceGFtcDguMVxcXFxodGRvY3NcXFxcY29tcG9uZW50c1xcXFxBdXRvU3luY1Byb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHlha3VzXFxcXERlc2t0b3BcXFxceGFtcDguMVxcXFxodGRvY3NcXFxcbm9kZV9tb2R1bGVzXFxcXHNvbm5lclxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Ccomponents%5C%5CAutoSyncProvider.tsx%22%2C%22ids%22%3A%5B%22AutoSyncProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"e2827d829acf\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzPzZmNGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlMjgyN2Q4MjlhY2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/AutoSyncProvider.tsx":
/*!*****************************************!*\
  !*** ./components/AutoSyncProvider.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSyncProvider: function() { return /* binding */ AutoSyncProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useAutoSync__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAutoSync */ \"(app-pages-browser)/./hooks/useAutoSync.ts\");\n/* __next_internal_client_entry_do_not_use__ AutoSyncProvider auto */ \nvar _s = $RefreshSig$();\n\nfunction AutoSyncProvider(param) {\n    let { children } = param;\n    _s();\n    (0,_hooks_useAutoSync__WEBPACK_IMPORTED_MODULE_1__.useAutoSync)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AutoSyncProvider, \"HP8f6TNAGwJ2trrllL6uC63JpYM=\", false, function() {\n    return [\n        _hooks_useAutoSync__WEBPACK_IMPORTED_MODULE_1__.useAutoSync\n    ];\n});\n_c = AutoSyncProvider;\nvar _c;\n$RefreshReg$(_c, \"AutoSyncProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvQXV0b1N5bmNQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUQ7QUFFMUMsU0FBU0MsaUJBQWlCLEtBQTJDO1FBQTNDLEVBQUVDLFFBQVEsRUFBaUMsR0FBM0M7O0lBQy9CRiwrREFBV0E7SUFDWCxxQkFBTztrQkFBR0U7O0FBQ1o7R0FIZ0JEOztRQUNkRCwyREFBV0E7OztLQURHQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0F1dG9TeW5jUHJvdmlkZXIudHN4PzM5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUF1dG9TeW5jIH0gZnJvbSAnQC9ob29rcy91c2VBdXRvU3luYydcblxuZXhwb3J0IGZ1bmN0aW9uIEF1dG9TeW5jUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICB1c2VBdXRvU3luYygpXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VBdXRvU3luYyIsIkF1dG9TeW5jUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AutoSyncProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useAutoSync.ts":
/*!******************************!*\
  !*** ./hooks/useAutoSync.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoSync: function() { return /* binding */ useAutoSync; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAutoSync auto */ \nfunction useAutoSync() {\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Função para executar a sincronização\n        const executarSincronizacao = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD04 Executando sincroniza\\xe7\\xe3o autom\\xe1tica...\");\n                const response = await fetch(\"/api/sync\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        action: \"sync-now\"\n                    })\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    console.log(\"✅ Sincroniza\\xe7\\xe3o autom\\xe1tica conclu\\xedda:\", result);\n                } else {\n                    console.error(\"❌ Erro na sincroniza\\xe7\\xe3o autom\\xe1tica:\", response.status);\n                }\n            } catch (error) {\n                console.error(\"❌ Erro na sincroniza\\xe7\\xe3o autom\\xe1tica:\", error);\n            }\n        };\n        // Executar sincronização imediatamente ao carregar\n        executarSincronizacao();\n        // Configurar intervalo de 2 horas (2 * 60 * 60 * 1000 = 7200000ms)\n        intervalRef.current = setInterval(executarSincronizacao, 2 * 60 * 60 * 1000);\n        // Cleanup ao desmontar\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n        };\n    }, []);\n    return null;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useAutoSync.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/NTlkZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: function() { return /* binding */ Toaster; },\n/* harmony export */   toast: function() { return /* binding */ toast; },\n/* harmony export */   useSonner: function() { return /* binding */ useSonner; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\nfunction __insertCSS(code) {\n    if (!code || typeof document == \"undefined\") return;\n    let head = document.head || document.getElementsByTagName(\"head\")[0];\n    let style = document.createElement(\"style\");\n    style.type = \"text/css\";\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case \"success\":\n            return SuccessIcon;\n        case \"info\":\n            return InfoIcon;\n        case \"warning\":\n            return WarningIcon;\n        case \"error\":\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = (param)=>{\n    let { visible, className } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            className\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: \"spinner-bar-\".concat(i)\n        }))));\n};\n_c = Loader;\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    _s();\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const callback = ()=>{\n            setIsDocumentHidden(document.hidden);\n        };\n        document.addEventListener(\"visibilitychange\", callback);\n        return ()=>window.removeEventListener(\"visibilitychange\", callback);\n    }, []);\n    return isDocumentHidden;\n};\n_s(useIsDocumentHidden, \"RJwWklAunJjdVVAElZ/SoraKxVU=\");\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === \"number\" || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: \"error\"\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"success\",\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"info\",\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"warning\",\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: \"loading\",\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: \"loading\",\n                    message: data.loading,\n                    description: typeof data.description !== \"function\" ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    \"resolve\",\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: \"default\",\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === \"function\" ? await data.error(\"HTTP error! status: \".concat(response.status)) : data.error;\n                    const description = typeof data.description === \"function\" ? await data.description(\"HTTP error! status: \".concat(response.status)) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"error\",\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === \"function\" ? await data.error(response) : data.error;\n                    const description = typeof data.description === \"function\" ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"error\",\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === \"function\" ? await data.success(response) : data.success;\n                    const description = typeof data.description === \"function\" ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"success\",\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    \"reject\",\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === \"function\" ? await data.error(error) : data.error;\n                    const description = typeof data.description === \"function\" ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === \"object\" && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: \"error\",\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === \"reject\" ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== \"string\" && typeof id !== \"number\") {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === \"object\" && \"ok\" in data && typeof data.ok === \"boolean\" && \"status\" in data && typeof data.status === \"number\";\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = \"24px\";\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = \"16px\";\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter(Boolean).join(\" \");\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split(\"-\");\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    _s1();\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = \"\", descriptionClassName = \"\", duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = \"Close toast\" } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || \"\";\n    const toastDescriptionClassname = toast.descriptionClassName || \"\";\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>heights.findIndex((height)=>height.toastId === toast.id) || 0, [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var _toast_closeButton;\n        return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n    }, [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>toast.duration || durationFromToaster || TOAST_LIFETIME, [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split(\"-\");\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return heights.reduce((prev, curr, reducerIndex)=>{\n            // Calculate offset up until current toast\n            if (reducerIndex >= heightIndex) {\n                return prev;\n            }\n            return prev + curr.height;\n        }, 0);\n    }, [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === \"loading\";\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>heightIndex * gap + toastsHeightBefore, [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        remainingTime.current = duration;\n    }, [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        setMounted(true);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const toastNode = toastRef.current;\n        if (toastNode) {\n            const height = toastNode.getBoundingClientRect().height;\n            // Add toast height to heights array after the toast is mounted\n            setInitialHeight(height);\n            setHeights((h)=>[\n                    {\n                        toastId: toast.id,\n                        height,\n                        position: toast.position\n                    },\n                    ...h\n                ]);\n            return ()=>setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        }\n    }, [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        // Keep height up to date with the content in case it updates\n        if (!mounted) return;\n        const toastNode = toastRef.current;\n        const originalHeight = toastNode.style.height;\n        toastNode.style.height = \"auto\";\n        const newHeight = toastNode.getBoundingClientRect().height;\n        toastNode.style.height = originalHeight;\n        setInitialHeight(newHeight);\n        setHeights((heights)=>{\n            const alreadyExists = heights.find((height)=>height.toastId === toast.id);\n            if (!alreadyExists) {\n                return [\n                    {\n                        toastId: toast.id,\n                        height: newHeight,\n                        position: toast.position\n                    },\n                    ...heights\n                ];\n            } else {\n                return heights.map((height)=>height.toastId === toast.id ? {\n                        ...height,\n                        height: newHeight\n                    } : height);\n            }\n        });\n    }, [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // Save the offset for the exit swipe animation\n        setRemoved(true);\n        setOffsetBeforeRemove(offset.current);\n        setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        setTimeout(()=>{\n            removeToast(toast);\n        }, TIME_BEFORE_UNMOUNT);\n    }, [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (toast.promise && toastType === \"loading\" || toast.duration === Infinity || toast.type === \"loading\") return;\n        let timeoutId;\n        // Pause the timer on each hover\n        const pauseTimer = ()=>{\n            if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                // Get the elapsed time since the timer started\n                const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                remainingTime.current = remainingTime.current - elapsedTime;\n            }\n            lastCloseTimerStartTimeRef.current = new Date().getTime();\n        };\n        const startTimer = ()=>{\n            // setTimeout(, Infinity) behaves as if the delay is 0.\n            // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n            // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n            if (remainingTime.current === Infinity) return;\n            closeTimerStartTimeRef.current = new Date().getTime();\n            // Let the toast know it has started\n            timeoutId = setTimeout(()=>{\n                toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                deleteToast();\n            }, remainingTime.current);\n        };\n        if (expanded || interacting || isDocumentHidden) {\n            pauseTimer();\n        } else {\n            startTimer();\n        }\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (toast.delete) {\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        }\n    }, [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, \"sonner-loader\"),\n                \"data-visible\": toastType === \"loading\"\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === \"loading\"\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            \"--index\": index,\n            \"--toasts-before\": index,\n            \"--z-index\": toasts.length - index,\n            \"--offset\": \"\".concat(removed ? offsetBeforeRemove : offset.current, \"px\"),\n            \"--initial-height\": expandByDefault ? \"auto\" : \"\".concat(initialHeight, \"px\"),\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (event.button === 2) return; // Return early on right click\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === \"BUTTON\") return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\", \"\")) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\", \"\")) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === \"x\" ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === \"x\") {\n                    setSwipeOutDirection(swipeAmountX > 0 ? \"right\" : \"left\");\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? \"down\" : \"up\");\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty(\"--swipe-amount-x\", \"0px\");\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty(\"--swipe-amount-y\", \"0px\");\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? \"x\" : \"y\");\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === \"y\") {\n                // Handle vertical swipes\n                if (swipeDirections.includes(\"top\") || swipeDirections.includes(\"bottom\")) {\n                    if (swipeDirections.includes(\"top\") && yDelta < 0 || swipeDirections.includes(\"bottom\") && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === \"x\") {\n                // Handle horizontal swipes\n                if (swipeDirections.includes(\"left\") || swipeDirections.includes(\"right\")) {\n                    if (swipeDirections.includes(\"left\") && xDelta < 0 || swipeDirections.includes(\"right\") && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty(\"--swipe-amount-x\", \"\".concat(swipeAmount.x, \"px\"));\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty(\"--swipe-amount-y\", \"\".concat(swipeAmount.y, \"px\"));\n        }\n    }, closeButton && !toast.jsx && toastType !== \"loading\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === \"loading\" && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== \"loading\" ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === \"function\" ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === \"function\" ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\n_s1(Toast, \"Hs2RwklMUctKsF2fEbXUzesmn3w=\", false, function() {\n    return [\n        useIsDocumentHidden\n    ];\n});\n_c1 = Toast;\nfunction getDocumentDirection() {\n    if (typeof window === \"undefined\") return \"ltr\";\n    if (typeof document === \"undefined\") return \"ltr\"; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute(\"dir\");\n    if (dirAttribute === \"auto\" || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? \"--mobile-offset\" : \"--offset\";\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((key)=>{\n                styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset === \"number\" ? \"\".concat(offset, \"px\") : offset;\n            });\n        }\n        if (typeof offset === \"number\" || typeof offset === \"string\") {\n            assignAll(offset);\n        } else if (typeof offset === \"object\") {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = defaultValue;\n                } else {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset[key] === \"number\" ? \"\".concat(offset[key], \"px\") : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    _s2();\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                setTimeout(()=>{\n                    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                        setActiveToasts((toasts)=>toasts.filter((t)=>t.id !== toast.id));\n                    });\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    setActiveToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, []);\n    return {\n        toasts: activeToasts\n    };\n}\n_s2(useSonner, \"wvKkrpl8d9UBJsfUcWYgFEOa7SA=\");\nconst Toaster = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s3(function Toaster(props, ref) {\n    _s3();\n    const { invert, position = \"bottom-right\", hotkey = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand, closeButton, className, offset, mobileOffset, theme = \"light\", richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = \"Notifications\" } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return Array.from(new Set([\n            position\n        ].concat(toasts.filter((toast)=>toast.position).map((toast)=>toast.position))));\n    }, [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== \"system\" ? theme : typeof window !== \"undefined\" ? window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\" : \"light\");\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((toastToRemove)=>{\n        setToasts((toasts)=>{\n            var _toasts_find;\n            if (!((_toasts_find = toasts.find((toast)=>toast.id === toastToRemove.id)) == null ? void 0 : _toasts_find.delete)) {\n                ToastState.dismiss(toastToRemove.id);\n            }\n            return toasts.filter((param)=>{\n                let { id } = param;\n                return id !== toastToRemove.id;\n            });\n        });\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                // Prevent batching of other state updates\n                requestAnimationFrame(()=>{\n                    setToasts((toasts)=>toasts.map((t)=>t.id === toast.id ? {\n                                ...t,\n                                delete: true\n                            } : t));\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>{\n                    setToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (theme !== \"system\") {\n            setActualTheme(theme);\n            return;\n        }\n        if (theme === \"system\") {\n            // check if current preference is dark\n            if (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches) {\n                // it's currently dark\n                setActualTheme(\"dark\");\n            } else {\n                // it's not dark\n                setActualTheme(\"light\");\n            }\n        }\n        if (typeof window === \"undefined\") return;\n        const darkMediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        try {\n            // Chrome & Firefox\n            darkMediaQuery.addEventListener(\"change\", (param)=>{\n                let { matches } = param;\n                if (matches) {\n                    setActualTheme(\"dark\");\n                } else {\n                    setActualTheme(\"light\");\n                }\n            });\n        } catch (error) {\n            // Safari < 14\n            darkMediaQuery.addListener((param)=>{\n                let { matches } = param;\n                try {\n                    if (matches) {\n                        setActualTheme(\"dark\");\n                    } else {\n                        setActualTheme(\"light\");\n                    }\n                } catch (e) {\n                    console.error(e);\n                }\n            });\n        }\n    }, [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Ensure expanded is always false when no toasts are present / only one left\n        if (toasts.length <= 1) {\n            setExpanded(false);\n        }\n    }, [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            var _listRef_current;\n            const isHotkeyPressed = hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) {\n                var _listRef_current1;\n                setExpanded(true);\n                (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n            }\n            if (event.code === \"Escape\" && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                setExpanded(false);\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (listRef.current) {\n            return ()=>{\n                if (lastFocusedElementRef.current) {\n                    lastFocusedElementRef.current.focus({\n                        preventScroll: true\n                    });\n                    lastFocusedElementRef.current = null;\n                    isFocusWithinRef.current = false;\n                }\n            };\n        }\n    }, [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": \"\".concat(containerAriaLabel, \" \").concat(hotkeyLabel),\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split(\"-\");\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === \"auto\" ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                \"--front-toast-height\": \"\".concat(((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0, \"px\"),\n                \"--width\": \"\".concat(TOAST_WIDTH, \"px\"),\n                \"--gap\": \"\".concat(gap, \"px\"),\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === \"false\";\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === \"false\";\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n}, \"oqEGKFhGV9uIBJI/pmW6D0z1xPo=\")), \"oqEGKFhGV9uIBJI/pmW6D0z1xPo=\");\n_c3 = Toaster;\n\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Loader\");\n$RefreshReg$(_c1, \"Toast\");\n$RefreshReg$(_c2, \"Toaster$React.forwardRef\");\n$RefreshReg$(_c3, \"Toaster\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/sonner/dist/index.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Ccomponents%5C%5CAutoSyncProvider.tsx%22%2C%22ids%22%3A%5B%22AutoSyncProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyakus%5C%5CDesktop%5C%5Cxamp8.1%5C%5Chtdocs%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);