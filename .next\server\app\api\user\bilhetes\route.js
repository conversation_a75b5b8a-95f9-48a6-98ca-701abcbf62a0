"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/bilhetes/route";
exports.ids = ["app/api/user/bilhetes/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fbilhetes%2Froute&page=%2Fapi%2Fuser%2Fbilhetes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fbilhetes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fbilhetes%2Froute&page=%2Fapi%2Fuser%2Fbilhetes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fbilhetes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_user_bilhetes_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/bilhetes/route.ts */ \"(rsc)/./app/api/user/bilhetes/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/bilhetes/route\",\n        pathname: \"/api/user/bilhetes\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/bilhetes/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\user\\\\bilhetes\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_user_bilhetes_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/user/bilhetes/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fbilhetes%2Froute&page=%2Fapi%2Fuser%2Fbilhetes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fbilhetes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/user/bilhetes/route.ts":
/*!****************************************!*\
  !*** ./app/api/user/bilhetes/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n\n\nconst dynamic = \"force-dynamic\";\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"user_id\");\n        console.log(\"\\uD83C\\uDFAB Buscando bilhetes para user_id:\", userId);\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"user_id \\xe9 obrigat\\xf3rio\"\n            }, {\n                status: 400\n            });\n        }\n        try {\n            await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n            // Buscar bilhetes do usuário com timeout\n            const bilhetes = await Promise.race([\n                (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n          SELECT\n            b.id,\n            b.codigo,\n            b.usuario_id,\n            b.usuario_nome,\n            b.usuario_email,\n            b.valor_total,\n            b.quantidade_apostas,\n            b.status,\n            b.transaction_id,\n            b.created_at\n          FROM bilhetes b\n          WHERE b.usuario_id = ?\n          ORDER BY b.created_at DESC\n          LIMIT 50\n        `, [\n                    userId\n                ]),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout na consulta de bilhetes\")), 5000))\n            ]);\n            console.log(\"\\uD83D\\uDCCA Bilhetes encontrados:\", bilhetes?.length || 0);\n            // Para cada bilhete, buscar as apostas relacionadas\n            const bilhetesFormatados = await Promise.all((bilhetes || []).map(async (bilhete)=>{\n                // Buscar apostas do bilhete na tabela bilhete_apostas\n                let apostas = [];\n                try {\n                    apostas = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n            SELECT\n              ba.resultado,\n              j.data_jogo,\n              tc.nome as time_casa_nome,\n              tc.nome_curto as time_casa_curto,\n              tf.nome as time_fora_nome,\n              tf.nome_curto as time_fora_curto,\n              c.nome as campeonato_nome\n            FROM bilhete_apostas ba\n            LEFT JOIN jogos j ON ba.match_id = j.id\n            LEFT JOIN times tc ON j.time_casa_id = tc.id\n            LEFT JOIN times tf ON j.time_fora_id = tf.id\n            LEFT JOIN campeonatos c ON j.campeonato_id = c.id\n            WHERE ba.bilhete_id = ?\n            ORDER BY j.data_jogo ASC\n          `, [\n                        bilhete.id\n                    ]);\n                } catch (apostasError) {\n                    console.warn(`⚠️ Erro ao buscar apostas do bilhete ${bilhete.id}:`, apostasError.message);\n                    apostas = [];\n                }\n                // Formatar apostas para o frontend\n                const apostasFormatadas = apostas.map((aposta)=>({\n                        jogo: `${aposta.time_casa_nome || \"Time Casa\"} x ${aposta.time_fora_nome || \"Time Fora\"}`,\n                        resultado: aposta.resultado === \"casa\" ? \"Casa\" : aposta.resultado === \"empate\" ? \"Empate\" : \"Fora\"\n                    }));\n                return {\n                    id: \"BLT\" + bilhete.id,\n                    codigo: bilhete.codigo,\n                    transaction_id: bilhete.transaction_id || bilhete.codigo,\n                    data: new Date(bilhete.created_at).toLocaleDateString(\"pt-BR\"),\n                    hora: new Date(bilhete.created_at).toLocaleTimeString(\"pt-BR\", {\n                        hour: \"2-digit\",\n                        minute: \"2-digit\"\n                    }),\n                    apostas: apostasFormatadas,\n                    valor: parseFloat(bilhete.valor_total),\n                    status: bilhete.status,\n                    premio: 0,\n                    qr_code_pix: \"\",\n                    db_id: bilhete.id\n                };\n            }));\n            console.log(`✅ ${bilhetesFormatados.length} bilhetes formatados para usuário ${userId}`);\n            // Log das apostas encontradas para debug\n            bilhetesFormatados.forEach((bilhete, index)=>{\n                console.log(`📋 Bilhete ${index + 1} (${bilhete.id}): ${bilhete.apostas.length} apostas`);\n                if (bilhete.apostas.length > 0) {\n                    bilhete.apostas.forEach((aposta, apostaIndex)=>{\n                        console.log(`   ${apostaIndex + 1}. ${aposta.jogo} - ${aposta.resultado}`);\n                    });\n                }\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                bilhetes: bilhetesFormatados,\n                total: bilhetesFormatados.length,\n                source: \"database\"\n            });\n        } catch (dbError) {\n            console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, dbError.message);\n            // Retornar dados básicos sem erro\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                bilhetes: [],\n                total: 0,\n                source: \"fallback\"\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Erro ao buscar bilhetes:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Erro interno do servidor\",\n            message: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/bilhetes/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fbilhetes%2Froute&page=%2Fapi%2Fuser%2Fbilhetes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fbilhetes%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();