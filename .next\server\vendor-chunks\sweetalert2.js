"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sweetalert2";
exports.ids = ["vendor-chunks/sweetalert2"];
exports.modules = {

/***/ "(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js":
/*!**************************************************************!*\
  !*** ./node_modules/sweetalert2/dist/sweetalert2.esm.all.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Swal)\n/* harmony export */ });\n/*!\n* sweetalert2 v11.22.2\n* Released under the MIT License.\n*/\nfunction _assertClassBrand(e, t, n) {\n  if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n  throw new TypeError(\"Private element is not present on this object\");\n}\nfunction _checkPrivateRedeclaration(e, t) {\n  if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n}\nfunction _classPrivateFieldGet2(s, a) {\n  return s.get(_assertClassBrand(s, a));\n}\nfunction _classPrivateFieldInitSpec(e, t, a) {\n  _checkPrivateRedeclaration(e, t), t.set(e, a);\n}\nfunction _classPrivateFieldSet2(s, a, r) {\n  return s.set(_assertClassBrand(s, a), r), r;\n}\n\nconst RESTORE_FOCUS_TIMEOUT = 100;\n\n/** @type {GlobalState} */\nconst globalState = {};\nconst focusPreviousActiveElement = () => {\n  if (globalState.previousActiveElement instanceof HTMLElement) {\n    globalState.previousActiveElement.focus();\n    globalState.previousActiveElement = null;\n  } else if (document.body) {\n    document.body.focus();\n  }\n};\n\n/**\n * Restore previous active (focused) element\n *\n * @param {boolean} returnFocus\n * @returns {Promise<void>}\n */\nconst restoreActiveElement = returnFocus => {\n  return new Promise(resolve => {\n    if (!returnFocus) {\n      return resolve();\n    }\n    const x = window.scrollX;\n    const y = window.scrollY;\n    globalState.restoreFocusTimeout = setTimeout(() => {\n      focusPreviousActiveElement();\n      resolve();\n    }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n    window.scrollTo(x, y);\n  });\n};\n\nconst swalPrefix = 'swal2-';\n\n/**\n * @typedef {Record<SwalClass, string>} SwalClasses\n */\n\n/**\n * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n * @typedef {Record<SwalIcon, string>} SwalIcons\n */\n\n/** @type {SwalClass[]} */\nconst classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error', 'draggable', 'dragging'];\nconst swalClasses = classNames.reduce((acc, className) => {\n  acc[className] = swalPrefix + className;\n  return acc;\n}, /** @type {SwalClasses} */{});\n\n/** @type {SwalIcon[]} */\nconst icons = ['success', 'warning', 'info', 'question', 'error'];\nconst iconTypes = icons.reduce((acc, icon) => {\n  acc[icon] = swalPrefix + icon;\n  return acc;\n}, /** @type {SwalIcons} */{});\n\nconst consolePrefix = 'SweetAlert2:';\n\n/**\n * Capitalize the first letter of a string\n *\n * @param {string} str\n * @returns {string}\n */\nconst capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n/**\n * Standardize console warnings\n *\n * @param {string | string[]} message\n */\nconst warn = message => {\n  console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n};\n\n/**\n * Standardize console errors\n *\n * @param {string} message\n */\nconst error = message => {\n  console.error(`${consolePrefix} ${message}`);\n};\n\n/**\n * Private global state for `warnOnce`\n *\n * @type {string[]}\n * @private\n */\nconst previousWarnOnceMessages = [];\n\n/**\n * Show a console warning, but only if it hasn't already been shown\n *\n * @param {string} message\n */\nconst warnOnce = message => {\n  if (!previousWarnOnceMessages.includes(message)) {\n    previousWarnOnceMessages.push(message);\n    warn(message);\n  }\n};\n\n/**\n * Show a one-time console warning about deprecated params/methods\n *\n * @param {string} deprecatedParam\n * @param {string?} useInstead\n */\nconst warnAboutDeprecation = (deprecatedParam, useInstead = null) => {\n  warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n};\n\n/**\n * If `arg` is a function, call it (with no arguments or context) and return the result.\n * Otherwise, just pass the value through\n *\n * @param {Function | any} arg\n * @returns {any}\n */\nconst callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n/**\n * @param {any} arg\n * @returns {boolean}\n */\nconst hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n/**\n * @param {any} arg\n * @returns {Promise<any>}\n */\nconst asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n/**\n * @param {any} arg\n * @returns {boolean}\n */\nconst isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n/**\n * Gets the popup container which contains the backdrop and the popup itself.\n *\n * @returns {HTMLElement | null}\n */\nconst getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n/**\n * @param {string} selectorString\n * @returns {HTMLElement | null}\n */\nconst elementBySelector = selectorString => {\n  const container = getContainer();\n  return container ? container.querySelector(selectorString) : null;\n};\n\n/**\n * @param {string} className\n * @returns {HTMLElement | null}\n */\nconst elementByClass = className => {\n  return elementBySelector(`.${className}`);\n};\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getPopup = () => elementByClass(swalClasses.popup);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getIcon = () => elementByClass(swalClasses.icon);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getTitle = () => elementByClass(swalClasses.title);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getImage = () => elementByClass(swalClasses.image);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getActions = () => elementByClass(swalClasses.actions);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getFooter = () => elementByClass(swalClasses.footer);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getCloseButton = () => elementByClass(swalClasses.close);\n\n// https://github.com/jkup/focusable/blob/master/index.js\nconst focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n/**\n * @returns {HTMLElement[]}\n */\nconst getFocusableElements = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return [];\n  }\n  /** @type {NodeListOf<HTMLElement>} */\n  const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n  const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n  // sort according to tabindex\n  .sort((a, b) => {\n    const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n    const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n    if (tabindexA > tabindexB) {\n      return 1;\n    } else if (tabindexA < tabindexB) {\n      return -1;\n    }\n    return 0;\n  });\n\n  /** @type {NodeListOf<HTMLElement>} */\n  const otherFocusableElements = popup.querySelectorAll(focusable);\n  const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n  return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n};\n\n/**\n * @returns {boolean}\n */\nconst isModal = () => {\n  return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n};\n\n/**\n * @returns {boolean}\n */\nconst isToast = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  return hasClass(popup, swalClasses.toast);\n};\n\n/**\n * @returns {boolean}\n */\nconst isLoading = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  return popup.hasAttribute('data-loading');\n};\n\n/**\n * Securely set innerHTML of an element\n * https://github.com/sweetalert2/sweetalert2/issues/1926\n *\n * @param {HTMLElement} elem\n * @param {string} html\n */\nconst setInnerHtml = (elem, html) => {\n  elem.textContent = '';\n  if (html) {\n    const parser = new DOMParser();\n    const parsed = parser.parseFromString(html, `text/html`);\n    const head = parsed.querySelector('head');\n    if (head) {\n      Array.from(head.childNodes).forEach(child => {\n        elem.appendChild(child);\n      });\n    }\n    const body = parsed.querySelector('body');\n    if (body) {\n      Array.from(body.childNodes).forEach(child => {\n        if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n          elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n        } else {\n          elem.appendChild(child);\n        }\n      });\n    }\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {string} className\n * @returns {boolean}\n */\nconst hasClass = (elem, className) => {\n  if (!className) {\n    return false;\n  }\n  const classList = className.split(/\\s+/);\n  for (let i = 0; i < classList.length; i++) {\n    if (!elem.classList.contains(classList[i])) {\n      return false;\n    }\n  }\n  return true;\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {SweetAlertOptions} params\n */\nconst removeCustomClasses = (elem, params) => {\n  Array.from(elem.classList).forEach(className => {\n    if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n      elem.classList.remove(className);\n    }\n  });\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {SweetAlertOptions} params\n * @param {string} className\n */\nconst applyCustomClass = (elem, params, className) => {\n  removeCustomClasses(elem, params);\n  if (!params.customClass) {\n    return;\n  }\n  const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n  if (!customClass) {\n    return;\n  }\n  if (typeof customClass !== 'string' && !customClass.forEach) {\n    warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n    return;\n  }\n  addClass(elem, customClass);\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n * @returns {HTMLInputElement | null}\n */\nconst getInput$1 = (popup, inputClass) => {\n  if (!inputClass) {\n    return null;\n  }\n  switch (inputClass) {\n    case 'select':\n    case 'textarea':\n    case 'file':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n    case 'checkbox':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n    case 'radio':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n    case 'range':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n    default:\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n  }\n};\n\n/**\n * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n */\nconst focusInput = input => {\n  input.focus();\n\n  // place cursor at end of text in text input\n  if (input.type !== 'file') {\n    // http://stackoverflow.com/a/2345915\n    const val = input.value;\n    input.value = '';\n    input.value = val;\n  }\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n * @param {boolean} condition\n */\nconst toggleClass = (target, classList, condition) => {\n  if (!target || !classList) {\n    return;\n  }\n  if (typeof classList === 'string') {\n    classList = classList.split(/\\s+/).filter(Boolean);\n  }\n  classList.forEach(className => {\n    if (Array.isArray(target)) {\n      target.forEach(elem => {\n        if (condition) {\n          elem.classList.add(className);\n        } else {\n          elem.classList.remove(className);\n        }\n      });\n    } else {\n      if (condition) {\n        target.classList.add(className);\n      } else {\n        target.classList.remove(className);\n      }\n    }\n  });\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n */\nconst addClass = (target, classList) => {\n  toggleClass(target, classList, true);\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n */\nconst removeClass = (target, classList) => {\n  toggleClass(target, classList, false);\n};\n\n/**\n * Get direct child of an element by class name\n *\n * @param {HTMLElement} elem\n * @param {string} className\n * @returns {HTMLElement | undefined}\n */\nconst getDirectChildByClass = (elem, className) => {\n  const children = Array.from(elem.children);\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    if (child instanceof HTMLElement && hasClass(child, className)) {\n      return child;\n    }\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {string} property\n * @param {*} value\n */\nconst applyNumericalStyle = (elem, property, value) => {\n  if (value === `${parseInt(value)}`) {\n    value = parseInt(value);\n  }\n  if (value || parseInt(value) === 0) {\n    elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n  } else {\n    elem.style.removeProperty(property);\n  }\n};\n\n/**\n * @param {HTMLElement | null} elem\n * @param {string} display\n */\nconst show = (elem, display = 'flex') => {\n  if (!elem) {\n    return;\n  }\n  elem.style.display = display;\n};\n\n/**\n * @param {HTMLElement | null} elem\n */\nconst hide = elem => {\n  if (!elem) {\n    return;\n  }\n  elem.style.display = 'none';\n};\n\n/**\n * @param {HTMLElement | null} elem\n * @param {string} display\n */\nconst showWhenInnerHtmlPresent = (elem, display = 'block') => {\n  if (!elem) {\n    return;\n  }\n  new MutationObserver(() => {\n    toggle(elem, elem.innerHTML, display);\n  }).observe(elem, {\n    childList: true,\n    subtree: true\n  });\n};\n\n/**\n * @param {HTMLElement} parent\n * @param {string} selector\n * @param {string} property\n * @param {string} value\n */\nconst setStyle = (parent, selector, property, value) => {\n  /** @type {HTMLElement | null} */\n  const el = parent.querySelector(selector);\n  if (el) {\n    el.style.setProperty(property, value);\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {any} condition\n * @param {string} display\n */\nconst toggle = (elem, condition, display = 'flex') => {\n  if (condition) {\n    show(elem, display);\n  } else {\n    hide(elem);\n  }\n};\n\n/**\n * borrowed from jquery $(elem).is(':visible') implementation\n *\n * @param {HTMLElement | null} elem\n * @returns {boolean}\n */\nconst isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n/**\n * @returns {boolean}\n */\nconst allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n/**\n * @param {HTMLElement} elem\n * @returns {boolean}\n */\nconst isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n/**\n * @param {HTMLElement} element\n * @param {HTMLElement} stopElement\n * @returns {boolean}\n */\nconst selfOrParentIsScrollable = (element, stopElement) => {\n  let parent = element;\n  while (parent && parent !== stopElement) {\n    if (isScrollable(parent)) {\n      return true;\n    }\n    parent = parent.parentElement;\n  }\n  return false;\n};\n\n/**\n * borrowed from https://stackoverflow.com/a/46352119\n *\n * @param {HTMLElement} elem\n * @returns {boolean}\n */\nconst hasCssAnimation = elem => {\n  const style = window.getComputedStyle(elem);\n  const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n  const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n  return animDuration > 0 || transDuration > 0;\n};\n\n/**\n * @param {number} timer\n * @param {boolean} reset\n */\nconst animateTimerProgressBar = (timer, reset = false) => {\n  const timerProgressBar = getTimerProgressBar();\n  if (!timerProgressBar) {\n    return;\n  }\n  if (isVisible$1(timerProgressBar)) {\n    if (reset) {\n      timerProgressBar.style.transition = 'none';\n      timerProgressBar.style.width = '100%';\n    }\n    setTimeout(() => {\n      timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n      timerProgressBar.style.width = '0%';\n    }, 10);\n  }\n};\nconst stopTimerProgressBar = () => {\n  const timerProgressBar = getTimerProgressBar();\n  if (!timerProgressBar) {\n    return;\n  }\n  const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n  timerProgressBar.style.removeProperty('transition');\n  timerProgressBar.style.width = '100%';\n  const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n  const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n  timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n};\n\n/**\n * Detect Node env\n *\n * @returns {boolean}\n */\nconst isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n\nconst sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n/**\n * @returns {boolean}\n */\nconst resetOldContainer = () => {\n  const oldContainer = getContainer();\n  if (!oldContainer) {\n    return false;\n  }\n  oldContainer.remove();\n  removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n  return true;\n};\nconst resetValidationMessage$1 = () => {\n  globalState.currentInstance.resetValidationMessage();\n};\nconst addInputChangeListeners = () => {\n  const popup = getPopup();\n  const input = getDirectChildByClass(popup, swalClasses.input);\n  const file = getDirectChildByClass(popup, swalClasses.file);\n  /** @type {HTMLInputElement} */\n  const range = popup.querySelector(`.${swalClasses.range} input`);\n  /** @type {HTMLOutputElement} */\n  const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n  const select = getDirectChildByClass(popup, swalClasses.select);\n  /** @type {HTMLInputElement} */\n  const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n  const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n  input.oninput = resetValidationMessage$1;\n  file.onchange = resetValidationMessage$1;\n  select.onchange = resetValidationMessage$1;\n  checkbox.onchange = resetValidationMessage$1;\n  textarea.oninput = resetValidationMessage$1;\n  range.oninput = () => {\n    resetValidationMessage$1();\n    rangeOutput.value = range.value;\n  };\n  range.onchange = () => {\n    resetValidationMessage$1();\n    rangeOutput.value = range.value;\n  };\n};\n\n/**\n * @param {string | HTMLElement} target\n * @returns {HTMLElement}\n */\nconst getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst setupAccessibility = params => {\n  const popup = getPopup();\n  popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n  popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n  if (!params.toast) {\n    popup.setAttribute('aria-modal', 'true');\n  }\n};\n\n/**\n * @param {HTMLElement} targetElement\n */\nconst setupRTL = targetElement => {\n  if (window.getComputedStyle(targetElement).direction === 'rtl') {\n    addClass(getContainer(), swalClasses.rtl);\n  }\n};\n\n/**\n * Add modal + backdrop + no-war message for Russians to DOM\n *\n * @param {SweetAlertOptions} params\n */\nconst init = params => {\n  // Clean up the old popup container if it exists\n  const oldContainerExisted = resetOldContainer();\n  if (isNodeEnv()) {\n    error('SweetAlert2 requires document to initialize');\n    return;\n  }\n  const container = document.createElement('div');\n  container.className = swalClasses.container;\n  if (oldContainerExisted) {\n    addClass(container, swalClasses['no-transition']);\n  }\n  setInnerHtml(container, sweetHTML);\n  container.dataset['swal2Theme'] = params.theme;\n  const targetElement = getTarget(params.target);\n  targetElement.appendChild(container);\n  if (params.topLayer) {\n    container.setAttribute('popover', '');\n    container.showPopover();\n  }\n  setupAccessibility(params);\n  setupRTL(targetElement);\n  addInputChangeListeners();\n};\n\n/**\n * @param {HTMLElement | object | string} param\n * @param {HTMLElement} target\n */\nconst parseHtmlToContainer = (param, target) => {\n  // DOM element\n  if (param instanceof HTMLElement) {\n    target.appendChild(param);\n  }\n\n  // Object\n  else if (typeof param === 'object') {\n    handleObject(param, target);\n  }\n\n  // Plain string\n  else if (param) {\n    setInnerHtml(target, param);\n  }\n};\n\n/**\n * @param {any} param\n * @param {HTMLElement} target\n */\nconst handleObject = (param, target) => {\n  // JQuery element(s)\n  if (param.jquery) {\n    handleJqueryElem(target, param);\n  }\n\n  // For other objects use their string representation\n  else {\n    setInnerHtml(target, param.toString());\n  }\n};\n\n/**\n * @param {HTMLElement} target\n * @param {any} elem\n */\nconst handleJqueryElem = (target, elem) => {\n  target.textContent = '';\n  if (0 in elem) {\n    for (let i = 0; i in elem; i++) {\n      target.appendChild(elem[i].cloneNode(true));\n    }\n  } else {\n    target.appendChild(elem.cloneNode(true));\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderActions = (instance, params) => {\n  const actions = getActions();\n  const loader = getLoader();\n  if (!actions || !loader) {\n    return;\n  }\n\n  // Actions (buttons) wrapper\n  if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n    hide(actions);\n  } else {\n    show(actions);\n  }\n\n  // Custom class\n  applyCustomClass(actions, params, 'actions');\n\n  // Render all the buttons\n  renderButtons(actions, loader, params);\n\n  // Loader\n  setInnerHtml(loader, params.loaderHtml || '');\n  applyCustomClass(loader, params, 'loader');\n};\n\n/**\n * @param {HTMLElement} actions\n * @param {HTMLElement} loader\n * @param {SweetAlertOptions} params\n */\nfunction renderButtons(actions, loader, params) {\n  const confirmButton = getConfirmButton();\n  const denyButton = getDenyButton();\n  const cancelButton = getCancelButton();\n  if (!confirmButton || !denyButton || !cancelButton) {\n    return;\n  }\n\n  // Render buttons\n  renderButton(confirmButton, 'confirm', params);\n  renderButton(denyButton, 'deny', params);\n  renderButton(cancelButton, 'cancel', params);\n  handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n  if (params.reverseButtons) {\n    if (params.toast) {\n      actions.insertBefore(cancelButton, confirmButton);\n      actions.insertBefore(denyButton, confirmButton);\n    } else {\n      actions.insertBefore(cancelButton, loader);\n      actions.insertBefore(denyButton, loader);\n      actions.insertBefore(confirmButton, loader);\n    }\n  }\n}\n\n/**\n * @param {HTMLElement} confirmButton\n * @param {HTMLElement} denyButton\n * @param {HTMLElement} cancelButton\n * @param {SweetAlertOptions} params\n */\nfunction handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n  if (!params.buttonsStyling) {\n    removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n    return;\n  }\n  addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n  // Apply custom background colors to action buttons\n  if (params.confirmButtonColor) {\n    confirmButton.style.setProperty('--swal2-confirm-button-background-color', params.confirmButtonColor);\n  }\n  if (params.denyButtonColor) {\n    denyButton.style.setProperty('--swal2-deny-button-background-color', params.denyButtonColor);\n  }\n  if (params.cancelButtonColor) {\n    cancelButton.style.setProperty('--swal2-cancel-button-background-color', params.cancelButtonColor);\n  }\n\n  // Apply the outline color to action buttons\n  applyOutlineColor(confirmButton);\n  applyOutlineColor(denyButton);\n  applyOutlineColor(cancelButton);\n}\n\n/**\n * @param {HTMLElement} button\n */\nfunction applyOutlineColor(button) {\n  const buttonStyle = window.getComputedStyle(button);\n  if (buttonStyle.getPropertyValue('--swal2-action-button-focus-box-shadow')) {\n    // If the button already has a custom outline color, no need to change it\n    return;\n  }\n  const outlineColor = buttonStyle.backgroundColor.replace(/rgba?\\((\\d+), (\\d+), (\\d+).*/, 'rgba($1, $2, $3, 0.5)');\n  button.style.setProperty('--swal2-action-button-focus-box-shadow', buttonStyle.getPropertyValue('--swal2-outline').replace(/ rgba\\(.*/, ` ${outlineColor}`));\n}\n\n/**\n * @param {HTMLElement} button\n * @param {'confirm' | 'deny' | 'cancel'} buttonType\n * @param {SweetAlertOptions} params\n */\nfunction renderButton(button, buttonType, params) {\n  const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n  toggle(button, params[`show${buttonName}Button`], 'inline-block');\n  setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n  button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n  // Add buttons custom classes\n  button.className = swalClasses[buttonType];\n  applyCustomClass(button, params, `${buttonType}Button`);\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderCloseButton = (instance, params) => {\n  const closeButton = getCloseButton();\n  if (!closeButton) {\n    return;\n  }\n  setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n  // Custom class\n  applyCustomClass(closeButton, params, 'closeButton');\n  toggle(closeButton, params.showCloseButton);\n  closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderContainer = (instance, params) => {\n  const container = getContainer();\n  if (!container) {\n    return;\n  }\n  handleBackdropParam(container, params.backdrop);\n  handlePositionParam(container, params.position);\n  handleGrowParam(container, params.grow);\n\n  // Custom class\n  applyCustomClass(container, params, 'container');\n};\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['backdrop']} backdrop\n */\nfunction handleBackdropParam(container, backdrop) {\n  if (typeof backdrop === 'string') {\n    container.style.background = backdrop;\n  } else if (!backdrop) {\n    addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n  }\n}\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['position']} position\n */\nfunction handlePositionParam(container, position) {\n  if (!position) {\n    return;\n  }\n  if (position in swalClasses) {\n    addClass(container, swalClasses[position]);\n  } else {\n    warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n    addClass(container, swalClasses.center);\n  }\n}\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['grow']} grow\n */\nfunction handleGrowParam(container, grow) {\n  if (!grow) {\n    return;\n  }\n  addClass(container, swalClasses[`grow-${grow}`]);\n}\n\n/**\n * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n * This is the approach that Babel will probably take to implement private methods/fields\n *   https://github.com/tc39/proposal-private-methods\n *   https://github.com/babel/babel/pull/7555\n * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n *   then we can use that language feature.\n */\n\nvar privateProps = {\n  innerParams: new WeakMap(),\n  domCache: new WeakMap()\n};\n\n/// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n\n/** @type {InputClass[]} */\nconst inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderInput = (instance, params) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const innerParams = privateProps.innerParams.get(instance);\n  const rerender = !innerParams || params.input !== innerParams.input;\n  inputClasses.forEach(inputClass => {\n    const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n    if (!inputContainer) {\n      return;\n    }\n\n    // set attributes\n    setAttributes(inputClass, params.inputAttributes);\n\n    // set class\n    inputContainer.className = swalClasses[inputClass];\n    if (rerender) {\n      hide(inputContainer);\n    }\n  });\n  if (params.input) {\n    if (rerender) {\n      showInput(params);\n    }\n    // set custom class\n    setCustomClass(params);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst showInput = params => {\n  if (!params.input) {\n    return;\n  }\n  if (!renderInputType[params.input]) {\n    error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n    return;\n  }\n  const inputContainer = getInputContainer(params.input);\n  if (!inputContainer) {\n    return;\n  }\n  const input = renderInputType[params.input](inputContainer, params);\n  show(inputContainer);\n\n  // input autofocus\n  if (params.inputAutoFocus) {\n    setTimeout(() => {\n      focusInput(input);\n    });\n  }\n};\n\n/**\n * @param {HTMLInputElement} input\n */\nconst removeAttributes = input => {\n  for (let i = 0; i < input.attributes.length; i++) {\n    const attrName = input.attributes[i].name;\n    if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n      input.removeAttribute(attrName);\n    }\n  }\n};\n\n/**\n * @param {InputClass} inputClass\n * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n */\nconst setAttributes = (inputClass, inputAttributes) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const input = getInput$1(popup, inputClass);\n  if (!input) {\n    return;\n  }\n  removeAttributes(input);\n  for (const attr in inputAttributes) {\n    input.setAttribute(attr, inputAttributes[attr]);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst setCustomClass = params => {\n  if (!params.input) {\n    return;\n  }\n  const inputContainer = getInputContainer(params.input);\n  if (inputContainer) {\n    applyCustomClass(inputContainer, params, 'input');\n  }\n};\n\n/**\n * @param {HTMLInputElement | HTMLTextAreaElement} input\n * @param {SweetAlertOptions} params\n */\nconst setInputPlaceholder = (input, params) => {\n  if (!input.placeholder && params.inputPlaceholder) {\n    input.placeholder = params.inputPlaceholder;\n  }\n};\n\n/**\n * @param {Input} input\n * @param {Input} prependTo\n * @param {SweetAlertOptions} params\n */\nconst setInputLabel = (input, prependTo, params) => {\n  if (params.inputLabel) {\n    const label = document.createElement('label');\n    const labelClass = swalClasses['input-label'];\n    label.setAttribute('for', input.id);\n    label.className = labelClass;\n    if (typeof params.customClass === 'object') {\n      addClass(label, params.customClass.inputLabel);\n    }\n    label.innerText = params.inputLabel;\n    prependTo.insertAdjacentElement('beforebegin', label);\n  }\n};\n\n/**\n * @param {SweetAlertInput} inputType\n * @returns {HTMLElement | undefined}\n */\nconst getInputContainer = inputType => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n};\n\n/**\n * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n * @param {SweetAlertOptions['inputValue']} inputValue\n */\nconst checkAndSetInputValue = (input, inputValue) => {\n  if (['string', 'number'].includes(typeof inputValue)) {\n    input.value = `${inputValue}`;\n  } else if (!isPromise(inputValue)) {\n    warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n  }\n};\n\n/** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\nconst renderInputType = {};\n\n/**\n * @param {HTMLInputElement} input\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n(input, params) => {\n  checkAndSetInputValue(input, params.inputValue);\n  setInputLabel(input, input, params);\n  setInputPlaceholder(input, params);\n  input.type = params.input;\n  return input;\n};\n\n/**\n * @param {HTMLInputElement} input\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.file = (input, params) => {\n  setInputLabel(input, input, params);\n  setInputPlaceholder(input, params);\n  return input;\n};\n\n/**\n * @param {HTMLInputElement} range\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.range = (range, params) => {\n  const rangeInput = range.querySelector('input');\n  const rangeOutput = range.querySelector('output');\n  checkAndSetInputValue(rangeInput, params.inputValue);\n  rangeInput.type = params.input;\n  checkAndSetInputValue(rangeOutput, params.inputValue);\n  setInputLabel(rangeInput, range, params);\n  return range;\n};\n\n/**\n * @param {HTMLSelectElement} select\n * @param {SweetAlertOptions} params\n * @returns {HTMLSelectElement}\n */\nrenderInputType.select = (select, params) => {\n  select.textContent = '';\n  if (params.inputPlaceholder) {\n    const placeholder = document.createElement('option');\n    setInnerHtml(placeholder, params.inputPlaceholder);\n    placeholder.value = '';\n    placeholder.disabled = true;\n    placeholder.selected = true;\n    select.appendChild(placeholder);\n  }\n  setInputLabel(select, select, params);\n  return select;\n};\n\n/**\n * @param {HTMLInputElement} radio\n * @returns {HTMLInputElement}\n */\nrenderInputType.radio = radio => {\n  radio.textContent = '';\n  return radio;\n};\n\n/**\n * @param {HTMLLabelElement} checkboxContainer\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.checkbox = (checkboxContainer, params) => {\n  const checkbox = getInput$1(getPopup(), 'checkbox');\n  checkbox.value = '1';\n  checkbox.checked = Boolean(params.inputValue);\n  const label = checkboxContainer.querySelector('span');\n  setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n  return checkbox;\n};\n\n/**\n * @param {HTMLTextAreaElement} textarea\n * @param {SweetAlertOptions} params\n * @returns {HTMLTextAreaElement}\n */\nrenderInputType.textarea = (textarea, params) => {\n  checkAndSetInputValue(textarea, params.inputValue);\n  setInputPlaceholder(textarea, params);\n  setInputLabel(textarea, textarea, params);\n\n  /**\n   * @param {HTMLElement} el\n   * @returns {number}\n   */\n  const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n  // https://github.com/sweetalert2/sweetalert2/issues/2291\n  setTimeout(() => {\n    // https://github.com/sweetalert2/sweetalert2/issues/1699\n    if ('MutationObserver' in window) {\n      const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n      const textareaResizeHandler = () => {\n        // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n        if (!document.body.contains(textarea)) {\n          return;\n        }\n        const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n        if (textareaWidth > initialPopupWidth) {\n          getPopup().style.width = `${textareaWidth}px`;\n        } else {\n          applyNumericalStyle(getPopup(), 'width', params.width);\n        }\n      };\n      new MutationObserver(textareaResizeHandler).observe(textarea, {\n        attributes: true,\n        attributeFilter: ['style']\n      });\n    }\n  });\n  return textarea;\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderContent = (instance, params) => {\n  const htmlContainer = getHtmlContainer();\n  if (!htmlContainer) {\n    return;\n  }\n  showWhenInnerHtmlPresent(htmlContainer);\n  applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n  // Content as HTML\n  if (params.html) {\n    parseHtmlToContainer(params.html, htmlContainer);\n    show(htmlContainer, 'block');\n  }\n\n  // Content as plain text\n  else if (params.text) {\n    htmlContainer.textContent = params.text;\n    show(htmlContainer, 'block');\n  }\n\n  // No content\n  else {\n    hide(htmlContainer);\n  }\n  renderInput(instance, params);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderFooter = (instance, params) => {\n  const footer = getFooter();\n  if (!footer) {\n    return;\n  }\n  showWhenInnerHtmlPresent(footer);\n  toggle(footer, params.footer, 'block');\n  if (params.footer) {\n    parseHtmlToContainer(params.footer, footer);\n  }\n\n  // Custom class\n  applyCustomClass(footer, params, 'footer');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderIcon = (instance, params) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  const icon = getIcon();\n  if (!icon) {\n    return;\n  }\n\n  // if the given icon already rendered, apply the styling without re-rendering the icon\n  if (innerParams && params.icon === innerParams.icon) {\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n    return;\n  }\n  if (!params.icon && !params.iconHtml) {\n    hide(icon);\n    return;\n  }\n  if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n    error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n    hide(icon);\n    return;\n  }\n  show(icon);\n\n  // Custom or default content\n  setContent(icon, params);\n  applyStyles(icon, params);\n\n  // Animate icon\n  addClass(icon, params.showClass && params.showClass.icon);\n\n  // Re-adjust the success icon on system theme change\n  const colorSchemeQueryList = window.matchMedia('(prefers-color-scheme: dark)');\n  colorSchemeQueryList.addEventListener('change', adjustSuccessIconBackgroundColor);\n};\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst applyStyles = (icon, params) => {\n  for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n    if (params.icon !== iconType) {\n      removeClass(icon, iconClassName);\n    }\n  }\n  addClass(icon, params.icon && iconTypes[params.icon]);\n\n  // Icon color\n  setColor(icon, params);\n\n  // Success icon background color\n  adjustSuccessIconBackgroundColor();\n\n  // Custom class\n  applyCustomClass(icon, params, 'icon');\n};\n\n// Adjust success icon background color to match the popup background color\nconst adjustSuccessIconBackgroundColor = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n  /** @type {NodeListOf<HTMLElement>} */\n  const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n  for (let i = 0; i < successIconParts.length; i++) {\n    successIconParts[i].style.backgroundColor = popupBackgroundColor;\n  }\n};\n\n/**\n *\n * @param {SweetAlertOptions} params\n * @returns {string}\n */\nconst successIconHtml = params => `\n  ${params.animation ? '<div class=\"swal2-success-circular-line-left\"></div>' : ''}\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div>\n  ${params.animation ? '<div class=\"swal2-success-fix\"></div>' : ''}\n  ${params.animation ? '<div class=\"swal2-success-circular-line-right\"></div>' : ''}\n`;\nconst errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst setContent = (icon, params) => {\n  if (!params.icon && !params.iconHtml) {\n    return;\n  }\n  let oldContent = icon.innerHTML;\n  let newContent = '';\n  if (params.iconHtml) {\n    newContent = iconContent(params.iconHtml);\n  } else if (params.icon === 'success') {\n    newContent = successIconHtml(params);\n    oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n  } else if (params.icon === 'error') {\n    newContent = errorIconHtml;\n  } else if (params.icon) {\n    const defaultIconHtml = {\n      question: '?',\n      warning: '!',\n      info: 'i'\n    };\n    newContent = iconContent(defaultIconHtml[params.icon]);\n  }\n  if (oldContent.trim() !== newContent.trim()) {\n    setInnerHtml(icon, newContent);\n  }\n};\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst setColor = (icon, params) => {\n  if (!params.iconColor) {\n    return;\n  }\n  icon.style.color = params.iconColor;\n  icon.style.borderColor = params.iconColor;\n  for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n    setStyle(icon, sel, 'background-color', params.iconColor);\n  }\n  setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n};\n\n/**\n * @param {string} content\n * @returns {string}\n */\nconst iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderImage = (instance, params) => {\n  const image = getImage();\n  if (!image) {\n    return;\n  }\n  if (!params.imageUrl) {\n    hide(image);\n    return;\n  }\n  show(image, '');\n\n  // Src, alt\n  image.setAttribute('src', params.imageUrl);\n  image.setAttribute('alt', params.imageAlt || '');\n\n  // Width, height\n  applyNumericalStyle(image, 'width', params.imageWidth);\n  applyNumericalStyle(image, 'height', params.imageHeight);\n\n  // Class\n  image.className = swalClasses.image;\n  applyCustomClass(image, params, 'image');\n};\n\nlet dragging = false;\nlet mousedownX = 0;\nlet mousedownY = 0;\nlet initialX = 0;\nlet initialY = 0;\n\n/**\n * @param {HTMLElement} popup\n */\nconst addDraggableListeners = popup => {\n  popup.addEventListener('mousedown', down);\n  document.body.addEventListener('mousemove', move);\n  popup.addEventListener('mouseup', up);\n  popup.addEventListener('touchstart', down);\n  document.body.addEventListener('touchmove', move);\n  popup.addEventListener('touchend', up);\n};\n\n/**\n * @param {HTMLElement} popup\n */\nconst removeDraggableListeners = popup => {\n  popup.removeEventListener('mousedown', down);\n  document.body.removeEventListener('mousemove', move);\n  popup.removeEventListener('mouseup', up);\n  popup.removeEventListener('touchstart', down);\n  document.body.removeEventListener('touchmove', move);\n  popup.removeEventListener('touchend', up);\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n */\nconst down = event => {\n  const popup = getPopup();\n  if (event.target === popup || getIcon().contains(/** @type {HTMLElement} */event.target)) {\n    dragging = true;\n    const clientXY = getClientXY(event);\n    mousedownX = clientXY.clientX;\n    mousedownY = clientXY.clientY;\n    initialX = parseInt(popup.style.insetInlineStart) || 0;\n    initialY = parseInt(popup.style.insetBlockStart) || 0;\n    addClass(popup, 'swal2-dragging');\n  }\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n */\nconst move = event => {\n  const popup = getPopup();\n  if (dragging) {\n    let {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    popup.style.insetInlineStart = `${initialX + (clientX - mousedownX)}px`;\n    popup.style.insetBlockStart = `${initialY + (clientY - mousedownY)}px`;\n  }\n};\nconst up = () => {\n  const popup = getPopup();\n  dragging = false;\n  removeClass(popup, 'swal2-dragging');\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n * @returns {{ clientX: number, clientY: number }}\n */\nconst getClientXY = event => {\n  let clientX = 0,\n    clientY = 0;\n  if (event.type.startsWith('mouse')) {\n    clientX = /** @type {MouseEvent} */event.clientX;\n    clientY = /** @type {MouseEvent} */event.clientY;\n  } else if (event.type.startsWith('touch')) {\n    clientX = /** @type {TouchEvent} */event.touches[0].clientX;\n    clientY = /** @type {TouchEvent} */event.touches[0].clientY;\n  }\n  return {\n    clientX,\n    clientY\n  };\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderPopup = (instance, params) => {\n  const container = getContainer();\n  const popup = getPopup();\n  if (!container || !popup) {\n    return;\n  }\n\n  // Width\n  // https://github.com/sweetalert2/sweetalert2/issues/2170\n  if (params.toast) {\n    applyNumericalStyle(container, 'width', params.width);\n    popup.style.width = '100%';\n    const loader = getLoader();\n    if (loader) {\n      popup.insertBefore(loader, getIcon());\n    }\n  } else {\n    applyNumericalStyle(popup, 'width', params.width);\n  }\n\n  // Padding\n  applyNumericalStyle(popup, 'padding', params.padding);\n\n  // Color\n  if (params.color) {\n    popup.style.color = params.color;\n  }\n\n  // Background\n  if (params.background) {\n    popup.style.background = params.background;\n  }\n  hide(getValidationMessage());\n\n  // Classes\n  addClasses$1(popup, params);\n  if (params.draggable && !params.toast) {\n    addClass(popup, swalClasses.draggable);\n    addDraggableListeners(popup);\n  } else {\n    removeClass(popup, swalClasses.draggable);\n    removeDraggableListeners(popup);\n  }\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} params\n */\nconst addClasses$1 = (popup, params) => {\n  const showClass = params.showClass || {};\n  // Default Class + showClass when updating Swal.update({})\n  popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n  if (params.toast) {\n    addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n    addClass(popup, swalClasses.toast);\n  } else {\n    addClass(popup, swalClasses.modal);\n  }\n\n  // Custom class\n  applyCustomClass(popup, params, 'popup');\n  // TODO: remove in the next major\n  if (typeof params.customClass === 'string') {\n    addClass(popup, params.customClass);\n  }\n\n  // Icon class (#1842)\n  if (params.icon) {\n    addClass(popup, swalClasses[`icon-${params.icon}`]);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderProgressSteps = (instance, params) => {\n  const progressStepsContainer = getProgressSteps();\n  if (!progressStepsContainer) {\n    return;\n  }\n  const {\n    progressSteps,\n    currentProgressStep\n  } = params;\n  if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n    hide(progressStepsContainer);\n    return;\n  }\n  show(progressStepsContainer);\n  progressStepsContainer.textContent = '';\n  if (currentProgressStep >= progressSteps.length) {\n    warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n  }\n  progressSteps.forEach((step, index) => {\n    const stepEl = createStepElement(step);\n    progressStepsContainer.appendChild(stepEl);\n    if (index === currentProgressStep) {\n      addClass(stepEl, swalClasses['active-progress-step']);\n    }\n    if (index !== progressSteps.length - 1) {\n      const lineEl = createLineElement(params);\n      progressStepsContainer.appendChild(lineEl);\n    }\n  });\n};\n\n/**\n * @param {string} step\n * @returns {HTMLLIElement}\n */\nconst createStepElement = step => {\n  const stepEl = document.createElement('li');\n  addClass(stepEl, swalClasses['progress-step']);\n  setInnerHtml(stepEl, step);\n  return stepEl;\n};\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {HTMLLIElement}\n */\nconst createLineElement = params => {\n  const lineEl = document.createElement('li');\n  addClass(lineEl, swalClasses['progress-step-line']);\n  if (params.progressStepsDistance) {\n    applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n  }\n  return lineEl;\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderTitle = (instance, params) => {\n  const title = getTitle();\n  if (!title) {\n    return;\n  }\n  showWhenInnerHtmlPresent(title);\n  toggle(title, params.title || params.titleText, 'block');\n  if (params.title) {\n    parseHtmlToContainer(params.title, title);\n  }\n  if (params.titleText) {\n    title.innerText = params.titleText;\n  }\n\n  // Custom class\n  applyCustomClass(title, params, 'title');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst render = (instance, params) => {\n  renderPopup(instance, params);\n  renderContainer(instance, params);\n  renderProgressSteps(instance, params);\n  renderIcon(instance, params);\n  renderImage(instance, params);\n  renderTitle(instance, params);\n  renderCloseButton(instance, params);\n  renderContent(instance, params);\n  renderActions(instance, params);\n  renderFooter(instance, params);\n  const popup = getPopup();\n  if (typeof params.didRender === 'function' && popup) {\n    params.didRender(popup);\n  }\n  globalState.eventEmitter.emit('didRender', popup);\n};\n\n/*\n * Global function to determine if SweetAlert2 popup is shown\n */\nconst isVisible = () => {\n  return isVisible$1(getPopup());\n};\n\n/*\n * Global function to click 'Confirm' button\n */\nconst clickConfirm = () => {\n  var _dom$getConfirmButton;\n  return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n};\n\n/*\n * Global function to click 'Deny' button\n */\nconst clickDeny = () => {\n  var _dom$getDenyButton;\n  return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n};\n\n/*\n * Global function to click 'Cancel' button\n */\nconst clickCancel = () => {\n  var _dom$getCancelButton;\n  return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n};\n\n/** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n/** @type {Record<DismissReason, DismissReason>} */\nconst DismissReason = Object.freeze({\n  cancel: 'cancel',\n  backdrop: 'backdrop',\n  close: 'close',\n  esc: 'esc',\n  timer: 'timer'\n});\n\n/**\n * @param {GlobalState} globalState\n */\nconst removeKeydownHandler = globalState => {\n  if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n    globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n      capture: globalState.keydownListenerCapture\n    });\n    globalState.keydownHandlerAdded = false;\n  }\n};\n\n/**\n * @param {GlobalState} globalState\n * @param {SweetAlertOptions} innerParams\n * @param {*} dismissWith\n */\nconst addKeydownHandler = (globalState, innerParams, dismissWith) => {\n  removeKeydownHandler(globalState);\n  if (!innerParams.toast) {\n    globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n    globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n    globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n    globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n      capture: globalState.keydownListenerCapture\n    });\n    globalState.keydownHandlerAdded = true;\n  }\n};\n\n/**\n * @param {number} index\n * @param {number} increment\n */\nconst setFocus = (index, increment) => {\n  var _dom$getPopup;\n  const focusableElements = getFocusableElements();\n  // search for visible elements and select the next possible match\n  if (focusableElements.length) {\n    index = index + increment;\n\n    // shift + tab when .swal2-popup is focused\n    if (index === -2) {\n      index = focusableElements.length - 1;\n    }\n\n    // rollover to first item\n    if (index === focusableElements.length) {\n      index = 0;\n\n      // go to last item\n    } else if (index === -1) {\n      index = focusableElements.length - 1;\n    }\n    focusableElements[index].focus();\n    return;\n  }\n  // no visible focusable elements, focus the popup\n  (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n};\nconst arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\nconst arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {KeyboardEvent} event\n * @param {Function} dismissWith\n */\nconst keydownHandler = (innerParams, event, dismissWith) => {\n  if (!innerParams) {\n    return; // This instance has already been destroyed\n  }\n\n  // Ignore keydown during IME composition\n  // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n  // https://github.com/sweetalert2/sweetalert2/issues/720\n  // https://github.com/sweetalert2/sweetalert2/issues/2406\n  if (event.isComposing || event.keyCode === 229) {\n    return;\n  }\n  if (innerParams.stopKeydownPropagation) {\n    event.stopPropagation();\n  }\n\n  // ENTER\n  if (event.key === 'Enter') {\n    handleEnter(event, innerParams);\n  }\n\n  // TAB\n  else if (event.key === 'Tab') {\n    handleTab(event);\n  }\n\n  // ARROWS - switch focus between buttons\n  else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n    handleArrows(event.key);\n  }\n\n  // ESC\n  else if (event.key === 'Escape') {\n    handleEsc(event, innerParams, dismissWith);\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n * @param {SweetAlertOptions} innerParams\n */\nconst handleEnter = (event, innerParams) => {\n  // https://github.com/sweetalert2/sweetalert2/issues/2386\n  if (!callIfFunction(innerParams.allowEnterKey)) {\n    return;\n  }\n  const input = getInput$1(getPopup(), innerParams.input);\n  if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n    if (['textarea', 'file'].includes(innerParams.input)) {\n      return; // do not submit\n    }\n    clickConfirm();\n    event.preventDefault();\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n */\nconst handleTab = event => {\n  const targetElement = event.target;\n  const focusableElements = getFocusableElements();\n  let btnIndex = -1;\n  for (let i = 0; i < focusableElements.length; i++) {\n    if (targetElement === focusableElements[i]) {\n      btnIndex = i;\n      break;\n    }\n  }\n\n  // Cycle to the next button\n  if (!event.shiftKey) {\n    setFocus(btnIndex, 1);\n  }\n\n  // Cycle to the prev button\n  else {\n    setFocus(btnIndex, -1);\n  }\n  event.stopPropagation();\n  event.preventDefault();\n};\n\n/**\n * @param {string} key\n */\nconst handleArrows = key => {\n  const actions = getActions();\n  const confirmButton = getConfirmButton();\n  const denyButton = getDenyButton();\n  const cancelButton = getCancelButton();\n  if (!actions || !confirmButton || !denyButton || !cancelButton) {\n    return;\n  }\n  /** @type HTMLElement[] */\n  const buttons = [confirmButton, denyButton, cancelButton];\n  if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n    return;\n  }\n  const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n  let buttonToFocus = document.activeElement;\n  if (!buttonToFocus) {\n    return;\n  }\n  for (let i = 0; i < actions.children.length; i++) {\n    buttonToFocus = buttonToFocus[sibling];\n    if (!buttonToFocus) {\n      return;\n    }\n    if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n      break;\n    }\n  }\n  if (buttonToFocus instanceof HTMLButtonElement) {\n    buttonToFocus.focus();\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n * @param {SweetAlertOptions} innerParams\n * @param {Function} dismissWith\n */\nconst handleEsc = (event, innerParams, dismissWith) => {\n  event.preventDefault();\n  if (callIfFunction(innerParams.allowEscapeKey)) {\n    dismissWith(DismissReason.esc);\n  }\n};\n\n/**\n * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n * This is the approach that Babel will probably take to implement private methods/fields\n *   https://github.com/tc39/proposal-private-methods\n *   https://github.com/babel/babel/pull/7555\n * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n *   then we can use that language feature.\n */\n\nvar privateMethods = {\n  swalPromiseResolve: new WeakMap(),\n  swalPromiseReject: new WeakMap()\n};\n\n// From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n// Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n// elements not within the active modal dialog will not be surfaced if a user opens a screen\n// reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\nconst setAriaHidden = () => {\n  const container = getContainer();\n  const bodyChildren = Array.from(document.body.children);\n  bodyChildren.forEach(el => {\n    if (el.contains(container)) {\n      return;\n    }\n    if (el.hasAttribute('aria-hidden')) {\n      el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n    }\n    el.setAttribute('aria-hidden', 'true');\n  });\n};\nconst unsetAriaHidden = () => {\n  const bodyChildren = Array.from(document.body.children);\n  bodyChildren.forEach(el => {\n    if (el.hasAttribute('data-previous-aria-hidden')) {\n      el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n      el.removeAttribute('data-previous-aria-hidden');\n    } else {\n      el.removeAttribute('aria-hidden');\n    }\n  });\n};\n\n// @ts-ignore\nconst isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n/**\n * Fix iOS scrolling\n * http://stackoverflow.com/q/39626302\n */\nconst iOSfix = () => {\n  if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n    const offset = document.body.scrollTop;\n    document.body.style.top = `${offset * -1}px`;\n    addClass(document.body, swalClasses.iosfix);\n    lockBodyScroll();\n  }\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1246\n */\nconst lockBodyScroll = () => {\n  const container = getContainer();\n  if (!container) {\n    return;\n  }\n  /** @type {boolean} */\n  let preventTouchMove;\n  /**\n   * @param {TouchEvent} event\n   */\n  container.ontouchstart = event => {\n    preventTouchMove = shouldPreventTouchMove(event);\n  };\n  /**\n   * @param {TouchEvent} event\n   */\n  container.ontouchmove = event => {\n    if (preventTouchMove) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  };\n};\n\n/**\n * @param {TouchEvent} event\n * @returns {boolean}\n */\nconst shouldPreventTouchMove = event => {\n  const target = event.target;\n  const container = getContainer();\n  const htmlContainer = getHtmlContainer();\n  if (!container || !htmlContainer) {\n    return false;\n  }\n  if (isStylus(event) || isZoom(event)) {\n    return false;\n  }\n  if (target === container) {\n    return true;\n  }\n  if (!isScrollable(container) && target instanceof HTMLElement && !selfOrParentIsScrollable(target, htmlContainer) &&\n  // #2823\n  target.tagName !== 'INPUT' &&\n  // #1603\n  target.tagName !== 'TEXTAREA' &&\n  // #2266\n  !(isScrollable(htmlContainer) &&\n  // #1944\n  htmlContainer.contains(target))) {\n    return true;\n  }\n  return false;\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1786\n *\n * @param {*} event\n * @returns {boolean}\n */\nconst isStylus = event => {\n  return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1891\n *\n * @param {TouchEvent} event\n * @returns {boolean}\n */\nconst isZoom = event => {\n  return event.touches && event.touches.length > 1;\n};\nconst undoIOSfix = () => {\n  if (hasClass(document.body, swalClasses.iosfix)) {\n    const offset = parseInt(document.body.style.top, 10);\n    removeClass(document.body, swalClasses.iosfix);\n    document.body.style.top = '';\n    document.body.scrollTop = offset * -1;\n  }\n};\n\n/**\n * Measure scrollbar width for padding body during modal show/hide\n * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n *\n * @returns {number}\n */\nconst measureScrollbar = () => {\n  const scrollDiv = document.createElement('div');\n  scrollDiv.className = swalClasses['scrollbar-measure'];\n  document.body.appendChild(scrollDiv);\n  const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n  document.body.removeChild(scrollDiv);\n  return scrollbarWidth;\n};\n\n/**\n * Remember state in cases where opening and handling a modal will fiddle with it.\n * @type {number | null}\n */\nlet previousBodyPadding = null;\n\n/**\n * @param {string} initialBodyOverflow\n */\nconst replaceScrollbarWithPadding = initialBodyOverflow => {\n  // for queues, do not do this more than once\n  if (previousBodyPadding !== null) {\n    return;\n  }\n  // if the body has overflow\n  if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n  ) {\n    // add padding so the content doesn't shift after removal of scrollbar\n    previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n    document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n  }\n};\nconst undoReplaceScrollbarWithPadding = () => {\n  if (previousBodyPadding !== null) {\n    document.body.style.paddingRight = `${previousBodyPadding}px`;\n    previousBodyPadding = null;\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} container\n * @param {boolean} returnFocus\n * @param {Function} didClose\n */\nfunction removePopupAndResetState(instance, container, returnFocus, didClose) {\n  if (isToast()) {\n    triggerDidCloseAndDispose(instance, didClose);\n  } else {\n    restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n    removeKeydownHandler(globalState);\n  }\n\n  // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n  // for some reason removing the container in Safari will scroll the document to bottom\n  if (isSafariOrIOS) {\n    container.setAttribute('style', 'display:none !important');\n    container.removeAttribute('class');\n    container.innerHTML = '';\n  } else {\n    container.remove();\n  }\n  if (isModal()) {\n    undoReplaceScrollbarWithPadding();\n    undoIOSfix();\n    unsetAriaHidden();\n  }\n  removeBodyClasses();\n}\n\n/**\n * Remove SweetAlert2 classes from body\n */\nfunction removeBodyClasses() {\n  removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n}\n\n/**\n * Instance method to close sweetAlert\n *\n * @param {any} resolveValue\n */\nfunction close(resolveValue) {\n  resolveValue = prepareResolveValue(resolveValue);\n  const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n  const didClose = triggerClosePopup(this);\n  if (this.isAwaitingPromise) {\n    // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n    if (!resolveValue.isDismissed) {\n      handleAwaitingPromise(this);\n      swalPromiseResolve(resolveValue);\n    }\n  } else if (didClose) {\n    // Resolve Swal promise\n    swalPromiseResolve(resolveValue);\n  }\n}\nconst triggerClosePopup = instance => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  const innerParams = privateProps.innerParams.get(instance);\n  if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n    return false;\n  }\n  removeClass(popup, innerParams.showClass.popup);\n  addClass(popup, innerParams.hideClass.popup);\n  const backdrop = getContainer();\n  removeClass(backdrop, innerParams.showClass.backdrop);\n  addClass(backdrop, innerParams.hideClass.backdrop);\n  handlePopupAnimation(instance, popup, innerParams);\n  return true;\n};\n\n/**\n * @param {any} error\n */\nfunction rejectPromise(error) {\n  const rejectPromise = privateMethods.swalPromiseReject.get(this);\n  handleAwaitingPromise(this);\n  if (rejectPromise) {\n    // Reject Swal promise\n    rejectPromise(error);\n  }\n}\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleAwaitingPromise = instance => {\n  if (instance.isAwaitingPromise) {\n    delete instance.isAwaitingPromise;\n    // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n    if (!privateProps.innerParams.get(instance)) {\n      instance._destroy();\n    }\n  }\n};\n\n/**\n * @param {any} resolveValue\n * @returns {SweetAlertResult}\n */\nconst prepareResolveValue = resolveValue => {\n  // When user calls Swal.close()\n  if (typeof resolveValue === 'undefined') {\n    return {\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: true\n    };\n  }\n  return Object.assign({\n    isConfirmed: false,\n    isDenied: false,\n    isDismissed: false\n  }, resolveValue);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} innerParams\n */\nconst handlePopupAnimation = (instance, popup, innerParams) => {\n  var _globalState$eventEmi;\n  const container = getContainer();\n  // If animation is supported, animate\n  const animationIsSupported = hasCssAnimation(popup);\n  if (typeof innerParams.willClose === 'function') {\n    innerParams.willClose(popup);\n  }\n  (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n  if (animationIsSupported) {\n    animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n  } else {\n    // Otherwise, remove immediately\n    removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} popup\n * @param {HTMLElement} container\n * @param {boolean} returnFocus\n * @param {Function} didClose\n */\nconst animatePopup = (instance, popup, container, returnFocus, didClose) => {\n  globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n  /**\n   * @param {AnimationEvent | TransitionEvent} e\n   */\n  const swalCloseAnimationFinished = function (e) {\n    if (e.target === popup) {\n      var _globalState$swalClos;\n      (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n      delete globalState.swalCloseEventFinishedCallback;\n      popup.removeEventListener('animationend', swalCloseAnimationFinished);\n      popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n    }\n  };\n  popup.addEventListener('animationend', swalCloseAnimationFinished);\n  popup.addEventListener('transitionend', swalCloseAnimationFinished);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {Function} didClose\n */\nconst triggerDidCloseAndDispose = (instance, didClose) => {\n  setTimeout(() => {\n    var _globalState$eventEmi2;\n    if (typeof didClose === 'function') {\n      didClose.bind(instance.params)();\n    }\n    (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n    // instance might have been destroyed already\n    if (instance._destroy) {\n      instance._destroy();\n    }\n  });\n};\n\n/**\n * Shows loader (spinner), this is useful with AJAX requests.\n * By default the loader be shown instead of the \"Confirm\" button.\n *\n * @param {HTMLButtonElement | null} [buttonToReplace]\n */\nconst showLoading = buttonToReplace => {\n  let popup = getPopup();\n  if (!popup) {\n    new Swal();\n  }\n  popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const loader = getLoader();\n  if (isToast()) {\n    hide(getIcon());\n  } else {\n    replaceButton(popup, buttonToReplace);\n  }\n  show(loader);\n  popup.setAttribute('data-loading', 'true');\n  popup.setAttribute('aria-busy', 'true');\n  popup.focus();\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {HTMLButtonElement | null} [buttonToReplace]\n */\nconst replaceButton = (popup, buttonToReplace) => {\n  const actions = getActions();\n  const loader = getLoader();\n  if (!actions || !loader) {\n    return;\n  }\n  if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n    buttonToReplace = getConfirmButton();\n  }\n  show(actions);\n  if (buttonToReplace) {\n    hide(buttonToReplace);\n    loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n    actions.insertBefore(loader, buttonToReplace);\n  }\n  addClass([popup, actions], swalClasses.loading);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputOptionsAndValue = (instance, params) => {\n  if (params.input === 'select' || params.input === 'radio') {\n    handleInputOptions(instance, params);\n  } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n    showLoading(getConfirmButton());\n    handleInputValue(instance, params);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} innerParams\n * @returns {SweetAlertInputValue}\n */\nconst getInputValue = (instance, innerParams) => {\n  const input = instance.getInput();\n  if (!input) {\n    return null;\n  }\n  switch (innerParams.input) {\n    case 'checkbox':\n      return getCheckboxValue(input);\n    case 'radio':\n      return getRadioValue(input);\n    case 'file':\n      return getFileValue(input);\n    default:\n      return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n  }\n};\n\n/**\n * @param {HTMLInputElement} input\n * @returns {number}\n */\nconst getCheckboxValue = input => input.checked ? 1 : 0;\n\n/**\n * @param {HTMLInputElement} input\n * @returns {string | null}\n */\nconst getRadioValue = input => input.checked ? input.value : null;\n\n/**\n * @param {HTMLInputElement} input\n * @returns {FileList | File | null}\n */\nconst getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputOptions = (instance, params) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  /**\n   * @param {Record<string, any>} inputOptions\n   */\n  const processInputOptions = inputOptions => {\n    if (params.input === 'select') {\n      populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n    } else if (params.input === 'radio') {\n      populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n    }\n  };\n  if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n    showLoading(getConfirmButton());\n    asPromise(params.inputOptions).then(inputOptions => {\n      instance.hideLoading();\n      processInputOptions(inputOptions);\n    });\n  } else if (typeof params.inputOptions === 'object') {\n    processInputOptions(params.inputOptions);\n  } else {\n    error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputValue = (instance, params) => {\n  const input = instance.getInput();\n  if (!input) {\n    return;\n  }\n  hide(input);\n  asPromise(params.inputValue).then(inputValue => {\n    input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n    show(input);\n    input.focus();\n    instance.hideLoading();\n  }).catch(err => {\n    error(`Error in inputValue promise: ${err}`);\n    input.value = '';\n    show(input);\n    input.focus();\n    instance.hideLoading();\n  });\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {InputOptionFlattened[]} inputOptions\n * @param {SweetAlertOptions} params\n */\nfunction populateSelectOptions(popup, inputOptions, params) {\n  const select = getDirectChildByClass(popup, swalClasses.select);\n  if (!select) {\n    return;\n  }\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} optionLabel\n   * @param {string} optionValue\n   */\n  const renderOption = (parent, optionLabel, optionValue) => {\n    const option = document.createElement('option');\n    option.value = optionValue;\n    setInnerHtml(option, optionLabel);\n    option.selected = isSelected(optionValue, params.inputValue);\n    parent.appendChild(option);\n  };\n  inputOptions.forEach(inputOption => {\n    const optionValue = inputOption[0];\n    const optionLabel = inputOption[1];\n    // <optgroup> spec:\n    // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n    // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n    // check whether this is a <optgroup>\n    if (Array.isArray(optionLabel)) {\n      // if it is an array, then it is an <optgroup>\n      const optgroup = document.createElement('optgroup');\n      optgroup.label = optionValue;\n      optgroup.disabled = false; // not configurable for now\n      select.appendChild(optgroup);\n      optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n    } else {\n      // case of <option>\n      renderOption(select, optionLabel, optionValue);\n    }\n  });\n  select.focus();\n}\n\n/**\n * @param {HTMLElement} popup\n * @param {InputOptionFlattened[]} inputOptions\n * @param {SweetAlertOptions} params\n */\nfunction populateRadioOptions(popup, inputOptions, params) {\n  const radio = getDirectChildByClass(popup, swalClasses.radio);\n  if (!radio) {\n    return;\n  }\n  inputOptions.forEach(inputOption => {\n    const radioValue = inputOption[0];\n    const radioLabel = inputOption[1];\n    const radioInput = document.createElement('input');\n    const radioLabelElement = document.createElement('label');\n    radioInput.type = 'radio';\n    radioInput.name = swalClasses.radio;\n    radioInput.value = radioValue;\n    if (isSelected(radioValue, params.inputValue)) {\n      radioInput.checked = true;\n    }\n    const label = document.createElement('span');\n    setInnerHtml(label, radioLabel);\n    label.className = swalClasses.label;\n    radioLabelElement.appendChild(radioInput);\n    radioLabelElement.appendChild(label);\n    radio.appendChild(radioLabelElement);\n  });\n  const radios = radio.querySelectorAll('input');\n  if (radios.length) {\n    radios[0].focus();\n  }\n}\n\n/**\n * Converts `inputOptions` into an array of `[value, label]`s\n *\n * @param {Record<string, any>} inputOptions\n * @typedef {string[]} InputOptionFlattened\n * @returns {InputOptionFlattened[]}\n */\nconst formatInputOptions = inputOptions => {\n  /** @type {InputOptionFlattened[]} */\n  const result = [];\n  if (inputOptions instanceof Map) {\n    inputOptions.forEach((value, key) => {\n      let valueFormatted = value;\n      if (typeof valueFormatted === 'object') {\n        // case of <optgroup>\n        valueFormatted = formatInputOptions(valueFormatted);\n      }\n      result.push([key, valueFormatted]);\n    });\n  } else {\n    Object.keys(inputOptions).forEach(key => {\n      let valueFormatted = inputOptions[key];\n      if (typeof valueFormatted === 'object') {\n        // case of <optgroup>\n        valueFormatted = formatInputOptions(valueFormatted);\n      }\n      result.push([key, valueFormatted]);\n    });\n  }\n  return result;\n};\n\n/**\n * @param {string} optionValue\n * @param {SweetAlertInputValue} inputValue\n * @returns {boolean}\n */\nconst isSelected = (optionValue, inputValue) => {\n  return !!inputValue && inputValue.toString() === optionValue.toString();\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleConfirmButtonClick = instance => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableButtons();\n  if (innerParams.input) {\n    handleConfirmOrDenyWithInput(instance, 'confirm');\n  } else {\n    confirm(instance, true);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleDenyButtonClick = instance => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableButtons();\n  if (innerParams.returnInputValueOnDeny) {\n    handleConfirmOrDenyWithInput(instance, 'deny');\n  } else {\n    deny(instance, false);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {Function} dismissWith\n */\nconst handleCancelButtonClick = (instance, dismissWith) => {\n  instance.disableButtons();\n  dismissWith(DismissReason.cancel);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {'confirm' | 'deny'} type\n */\nconst handleConfirmOrDenyWithInput = (instance, type) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  if (!innerParams.input) {\n    error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n    return;\n  }\n  const input = instance.getInput();\n  const inputValue = getInputValue(instance, innerParams);\n  if (innerParams.inputValidator) {\n    handleInputValidator(instance, inputValue, type);\n  } else if (input && !input.checkValidity()) {\n    instance.enableButtons();\n    instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n  } else if (type === 'deny') {\n    deny(instance, inputValue);\n  } else {\n    confirm(instance, inputValue);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertInputValue} inputValue\n * @param {'confirm' | 'deny'} type\n */\nconst handleInputValidator = (instance, inputValue, type) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableInput();\n  const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n  validationPromise.then(validationMessage => {\n    instance.enableButtons();\n    instance.enableInput();\n    if (validationMessage) {\n      instance.showValidationMessage(validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  });\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst deny = (instance, value) => {\n  const innerParams = privateProps.innerParams.get(instance || undefined);\n  if (innerParams.showLoaderOnDeny) {\n    showLoading(getDenyButton());\n  }\n  if (innerParams.preDeny) {\n    instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n    const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n    preDenyPromise.then(preDenyValue => {\n      if (preDenyValue === false) {\n        instance.hideLoading();\n        handleAwaitingPromise(instance);\n      } else {\n        instance.close({\n          isDenied: true,\n          value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n        });\n      }\n    }).catch(error => rejectWith(instance || undefined, error));\n  } else {\n    instance.close({\n      isDenied: true,\n      value\n    });\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst succeedWith = (instance, value) => {\n  instance.close({\n    isConfirmed: true,\n    value\n  });\n};\n\n/**\n *\n * @param {SweetAlert} instance\n * @param {string} error\n */\nconst rejectWith = (instance, error) => {\n  instance.rejectPromise(error);\n};\n\n/**\n *\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst confirm = (instance, value) => {\n  const innerParams = privateProps.innerParams.get(instance || undefined);\n  if (innerParams.showLoaderOnConfirm) {\n    showLoading();\n  }\n  if (innerParams.preConfirm) {\n    instance.resetValidationMessage();\n    instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n    const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n    preConfirmPromise.then(preConfirmValue => {\n      if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n        instance.hideLoading();\n        handleAwaitingPromise(instance);\n      } else {\n        succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n      }\n    }).catch(error => rejectWith(instance || undefined, error));\n  } else {\n    succeedWith(instance, value);\n  }\n};\n\n/**\n * Hides loader and shows back the button which was hidden by .showLoading()\n */\nfunction hideLoading() {\n  // do nothing if popup is closed\n  const innerParams = privateProps.innerParams.get(this);\n  if (!innerParams) {\n    return;\n  }\n  const domCache = privateProps.domCache.get(this);\n  hide(domCache.loader);\n  if (isToast()) {\n    if (innerParams.icon) {\n      show(getIcon());\n    }\n  } else {\n    showRelatedButton(domCache);\n  }\n  removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n  domCache.popup.removeAttribute('aria-busy');\n  domCache.popup.removeAttribute('data-loading');\n  domCache.confirmButton.disabled = false;\n  domCache.denyButton.disabled = false;\n  domCache.cancelButton.disabled = false;\n}\nconst showRelatedButton = domCache => {\n  const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n  if (buttonToReplace.length) {\n    show(buttonToReplace[0], 'inline-block');\n  } else if (allButtonsAreHidden()) {\n    hide(domCache.actions);\n  }\n};\n\n/**\n * Gets the input DOM node, this method works with input parameter.\n *\n * @returns {HTMLInputElement | null}\n */\nfunction getInput() {\n  const innerParams = privateProps.innerParams.get(this);\n  const domCache = privateProps.domCache.get(this);\n  if (!domCache) {\n    return null;\n  }\n  return getInput$1(domCache.popup, innerParams.input);\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {string[]} buttons\n * @param {boolean} disabled\n */\nfunction setButtonsDisabled(instance, buttons, disabled) {\n  const domCache = privateProps.domCache.get(instance);\n  buttons.forEach(button => {\n    domCache[button].disabled = disabled;\n  });\n}\n\n/**\n * @param {HTMLInputElement | null} input\n * @param {boolean} disabled\n */\nfunction setInputDisabled(input, disabled) {\n  const popup = getPopup();\n  if (!popup || !input) {\n    return;\n  }\n  if (input.type === 'radio') {\n    /** @type {NodeListOf<HTMLInputElement>} */\n    const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n    for (let i = 0; i < radios.length; i++) {\n      radios[i].disabled = disabled;\n    }\n  } else {\n    input.disabled = disabled;\n  }\n}\n\n/**\n * Enable all the buttons\n * @this {SweetAlert}\n */\nfunction enableButtons() {\n  setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n}\n\n/**\n * Disable all the buttons\n * @this {SweetAlert}\n */\nfunction disableButtons() {\n  setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n}\n\n/**\n * Enable the input field\n * @this {SweetAlert}\n */\nfunction enableInput() {\n  setInputDisabled(this.getInput(), false);\n}\n\n/**\n * Disable the input field\n * @this {SweetAlert}\n */\nfunction disableInput() {\n  setInputDisabled(this.getInput(), true);\n}\n\n/**\n * Show block with validation message\n *\n * @param {string} error\n * @this {SweetAlert}\n */\nfunction showValidationMessage(error) {\n  const domCache = privateProps.domCache.get(this);\n  const params = privateProps.innerParams.get(this);\n  setInnerHtml(domCache.validationMessage, error);\n  domCache.validationMessage.className = swalClasses['validation-message'];\n  if (params.customClass && params.customClass.validationMessage) {\n    addClass(domCache.validationMessage, params.customClass.validationMessage);\n  }\n  show(domCache.validationMessage);\n  const input = this.getInput();\n  if (input) {\n    input.setAttribute('aria-invalid', 'true');\n    input.setAttribute('aria-describedby', swalClasses['validation-message']);\n    focusInput(input);\n    addClass(input, swalClasses.inputerror);\n  }\n}\n\n/**\n * Hide block with validation message\n *\n * @this {SweetAlert}\n */\nfunction resetValidationMessage() {\n  const domCache = privateProps.domCache.get(this);\n  if (domCache.validationMessage) {\n    hide(domCache.validationMessage);\n  }\n  const input = this.getInput();\n  if (input) {\n    input.removeAttribute('aria-invalid');\n    input.removeAttribute('aria-describedby');\n    removeClass(input, swalClasses.inputerror);\n  }\n}\n\nconst defaultParams = {\n  title: '',\n  titleText: '',\n  text: '',\n  html: '',\n  footer: '',\n  icon: undefined,\n  iconColor: undefined,\n  iconHtml: undefined,\n  template: undefined,\n  toast: false,\n  draggable: false,\n  animation: true,\n  theme: 'light',\n  showClass: {\n    popup: 'swal2-show',\n    backdrop: 'swal2-backdrop-show',\n    icon: 'swal2-icon-show'\n  },\n  hideClass: {\n    popup: 'swal2-hide',\n    backdrop: 'swal2-backdrop-hide',\n    icon: 'swal2-icon-hide'\n  },\n  customClass: {},\n  target: 'body',\n  color: undefined,\n  backdrop: true,\n  heightAuto: true,\n  allowOutsideClick: true,\n  allowEscapeKey: true,\n  allowEnterKey: true,\n  stopKeydownPropagation: true,\n  keydownListenerCapture: false,\n  showConfirmButton: true,\n  showDenyButton: false,\n  showCancelButton: false,\n  preConfirm: undefined,\n  preDeny: undefined,\n  confirmButtonText: 'OK',\n  confirmButtonAriaLabel: '',\n  confirmButtonColor: undefined,\n  denyButtonText: 'No',\n  denyButtonAriaLabel: '',\n  denyButtonColor: undefined,\n  cancelButtonText: 'Cancel',\n  cancelButtonAriaLabel: '',\n  cancelButtonColor: undefined,\n  buttonsStyling: true,\n  reverseButtons: false,\n  focusConfirm: true,\n  focusDeny: false,\n  focusCancel: false,\n  returnFocus: true,\n  showCloseButton: false,\n  closeButtonHtml: '&times;',\n  closeButtonAriaLabel: 'Close this dialog',\n  loaderHtml: '',\n  showLoaderOnConfirm: false,\n  showLoaderOnDeny: false,\n  imageUrl: undefined,\n  imageWidth: undefined,\n  imageHeight: undefined,\n  imageAlt: '',\n  timer: undefined,\n  timerProgressBar: false,\n  width: undefined,\n  padding: undefined,\n  background: undefined,\n  input: undefined,\n  inputPlaceholder: '',\n  inputLabel: '',\n  inputValue: '',\n  inputOptions: {},\n  inputAutoFocus: true,\n  inputAutoTrim: true,\n  inputAttributes: {},\n  inputValidator: undefined,\n  returnInputValueOnDeny: false,\n  validationMessage: undefined,\n  grow: false,\n  position: 'center',\n  progressSteps: [],\n  currentProgressStep: undefined,\n  progressStepsDistance: undefined,\n  willOpen: undefined,\n  didOpen: undefined,\n  didRender: undefined,\n  willClose: undefined,\n  didClose: undefined,\n  didDestroy: undefined,\n  scrollbarPadding: true,\n  topLayer: false\n};\nconst updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'draggable', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'theme', 'willClose'];\n\n/** @type {Record<string, string | undefined>} */\nconst deprecatedParams = {\n  allowEnterKey: undefined\n};\nconst toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'draggable', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n/**\n * Is valid parameter\n *\n * @param {string} paramName\n * @returns {boolean}\n */\nconst isValidParameter = paramName => {\n  return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n};\n\n/**\n * Is valid parameter for Swal.update() method\n *\n * @param {string} paramName\n * @returns {boolean}\n */\nconst isUpdatableParameter = paramName => {\n  return updatableParams.indexOf(paramName) !== -1;\n};\n\n/**\n * Is deprecated parameter\n *\n * @param {string} paramName\n * @returns {string | undefined}\n */\nconst isDeprecatedParameter = paramName => {\n  return deprecatedParams[paramName];\n};\n\n/**\n * @param {string} param\n */\nconst checkIfParamIsValid = param => {\n  if (!isValidParameter(param)) {\n    warn(`Unknown parameter \"${param}\"`);\n  }\n};\n\n/**\n * @param {string} param\n */\nconst checkIfToastParamIsValid = param => {\n  if (toastIncompatibleParams.includes(param)) {\n    warn(`The parameter \"${param}\" is incompatible with toasts`);\n  }\n};\n\n/**\n * @param {string} param\n */\nconst checkIfParamIsDeprecated = param => {\n  const isDeprecated = isDeprecatedParameter(param);\n  if (isDeprecated) {\n    warnAboutDeprecation(param, isDeprecated);\n  }\n};\n\n/**\n * Show relevant warnings for given params\n *\n * @param {SweetAlertOptions} params\n */\nconst showWarningsForParams = params => {\n  if (params.backdrop === false && params.allowOutsideClick) {\n    warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n  }\n  if (params.theme && !['light', 'dark', 'auto', 'minimal', 'borderless', 'embed-iframe', 'bulma', 'bulma-light', 'bulma-dark'].includes(params.theme)) {\n    warn(`Invalid theme \"${params.theme}\"`);\n  }\n  for (const param in params) {\n    checkIfParamIsValid(param);\n    if (params.toast) {\n      checkIfToastParamIsValid(param);\n    }\n    checkIfParamIsDeprecated(param);\n  }\n};\n\n/**\n * Updates popup parameters.\n *\n * @param {SweetAlertOptions} params\n */\nfunction update(params) {\n  const container = getContainer();\n  const popup = getPopup();\n  const innerParams = privateProps.innerParams.get(this);\n  if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n    warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n    return;\n  }\n  const validUpdatableParams = filterValidParams(params);\n  const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n  showWarningsForParams(updatedParams);\n  container.dataset['swal2Theme'] = updatedParams.theme;\n  render(this, updatedParams);\n  privateProps.innerParams.set(this, updatedParams);\n  Object.defineProperties(this, {\n    params: {\n      value: Object.assign({}, this.params, params),\n      writable: false,\n      enumerable: true\n    }\n  });\n}\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {SweetAlertOptions}\n */\nconst filterValidParams = params => {\n  const validUpdatableParams = {};\n  Object.keys(params).forEach(param => {\n    if (isUpdatableParameter(param)) {\n      validUpdatableParams[param] = params[param];\n    } else {\n      warn(`Invalid parameter to update: ${param}`);\n    }\n  });\n  return validUpdatableParams;\n};\n\n/**\n * Dispose the current SweetAlert2 instance\n */\nfunction _destroy() {\n  const domCache = privateProps.domCache.get(this);\n  const innerParams = privateProps.innerParams.get(this);\n  if (!innerParams) {\n    disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n    return; // This instance has already been destroyed\n  }\n\n  // Check if there is another Swal closing\n  if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n    globalState.swalCloseEventFinishedCallback();\n    delete globalState.swalCloseEventFinishedCallback;\n  }\n  if (typeof innerParams.didDestroy === 'function') {\n    innerParams.didDestroy();\n  }\n  globalState.eventEmitter.emit('didDestroy');\n  disposeSwal(this);\n}\n\n/**\n * @param {SweetAlert} instance\n */\nconst disposeSwal = instance => {\n  disposeWeakMaps(instance);\n  // Unset this.params so GC will dispose it (#1569)\n  delete instance.params;\n  // Unset globalState props so GC will dispose globalState (#1569)\n  delete globalState.keydownHandler;\n  delete globalState.keydownTarget;\n  // Unset currentInstance\n  delete globalState.currentInstance;\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst disposeWeakMaps = instance => {\n  // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n  if (instance.isAwaitingPromise) {\n    unsetWeakMaps(privateProps, instance);\n    instance.isAwaitingPromise = true;\n  } else {\n    unsetWeakMaps(privateMethods, instance);\n    unsetWeakMaps(privateProps, instance);\n    delete instance.isAwaitingPromise;\n    // Unset instance methods\n    delete instance.disableButtons;\n    delete instance.enableButtons;\n    delete instance.getInput;\n    delete instance.disableInput;\n    delete instance.enableInput;\n    delete instance.hideLoading;\n    delete instance.disableLoading;\n    delete instance.showValidationMessage;\n    delete instance.resetValidationMessage;\n    delete instance.close;\n    delete instance.closePopup;\n    delete instance.closeModal;\n    delete instance.closeToast;\n    delete instance.rejectPromise;\n    delete instance.update;\n    delete instance._destroy;\n  }\n};\n\n/**\n * @param {object} obj\n * @param {SweetAlert} instance\n */\nconst unsetWeakMaps = (obj, instance) => {\n  for (const i in obj) {\n    obj[i].delete(instance);\n  }\n};\n\nvar instanceMethods = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  _destroy: _destroy,\n  close: close,\n  closeModal: close,\n  closePopup: close,\n  closeToast: close,\n  disableButtons: disableButtons,\n  disableInput: disableInput,\n  disableLoading: hideLoading,\n  enableButtons: enableButtons,\n  enableInput: enableInput,\n  getInput: getInput,\n  handleAwaitingPromise: handleAwaitingPromise,\n  hideLoading: hideLoading,\n  rejectPromise: rejectPromise,\n  resetValidationMessage: resetValidationMessage,\n  showValidationMessage: showValidationMessage,\n  update: update\n});\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handlePopupClick = (innerParams, domCache, dismissWith) => {\n  if (innerParams.toast) {\n    handleToastClick(innerParams, domCache, dismissWith);\n  } else {\n    // Ignore click events that had mousedown on the popup but mouseup on the container\n    // This can happen when the user drags a slider\n    handleModalMousedown(domCache);\n\n    // Ignore click events that had mousedown on the container but mouseup on the popup\n    handleContainerMousedown(domCache);\n    handleModalClick(innerParams, domCache, dismissWith);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handleToastClick = (innerParams, domCache, dismissWith) => {\n  // Closing toast by internal click\n  domCache.popup.onclick = () => {\n    if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n      return;\n    }\n    dismissWith(DismissReason.close);\n  };\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @returns {boolean}\n */\nconst isAnyButtonShown = innerParams => {\n  return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n};\nlet ignoreOutsideClick = false;\n\n/**\n * @param {DomCache} domCache\n */\nconst handleModalMousedown = domCache => {\n  domCache.popup.onmousedown = () => {\n    domCache.container.onmouseup = function (e) {\n      domCache.container.onmouseup = () => {};\n      // We only check if the mouseup target is the container because usually it doesn't\n      // have any other direct children aside of the popup\n      if (e.target === domCache.container) {\n        ignoreOutsideClick = true;\n      }\n    };\n  };\n};\n\n/**\n * @param {DomCache} domCache\n */\nconst handleContainerMousedown = domCache => {\n  domCache.container.onmousedown = e => {\n    // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n    if (e.target === domCache.container) {\n      e.preventDefault();\n    }\n    domCache.popup.onmouseup = function (e) {\n      domCache.popup.onmouseup = () => {};\n      // We also need to check if the mouseup target is a child of the popup\n      if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n        ignoreOutsideClick = true;\n      }\n    };\n  };\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handleModalClick = (innerParams, domCache, dismissWith) => {\n  domCache.container.onclick = e => {\n    if (ignoreOutsideClick) {\n      ignoreOutsideClick = false;\n      return;\n    }\n    if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n      dismissWith(DismissReason.backdrop);\n    }\n  };\n};\n\nconst isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\nconst isElement = elem => elem instanceof Element || isJqueryElement(elem);\nconst argsToParams = args => {\n  const params = {};\n  if (typeof args[0] === 'object' && !isElement(args[0])) {\n    Object.assign(params, args[0]);\n  } else {\n    ['title', 'html', 'icon'].forEach((name, index) => {\n      const arg = args[index];\n      if (typeof arg === 'string' || isElement(arg)) {\n        params[name] = arg;\n      } else if (arg !== undefined) {\n        error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n      }\n    });\n  }\n  return params;\n};\n\n/**\n * Main method to create a new SweetAlert2 popup\n *\n * @param  {...SweetAlertOptions} args\n * @returns {Promise<SweetAlertResult>}\n */\nfunction fire(...args) {\n  return new this(...args);\n}\n\n/**\n * Returns an extended version of `Swal` containing `params` as defaults.\n * Useful for reusing Swal configuration.\n *\n * For example:\n *\n * Before:\n * const textPromptOptions = { input: 'text', showCancelButton: true }\n * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n *\n * After:\n * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n * const {value: firstName} = await TextPrompt('What is your first name?')\n * const {value: lastName} = await TextPrompt('What is your last name?')\n *\n * @param {SweetAlertOptions} mixinParams\n * @returns {SweetAlert}\n */\nfunction mixin(mixinParams) {\n  class MixinSwal extends this {\n    _main(params, priorityMixinParams) {\n      return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n    }\n  }\n  // @ts-ignore\n  return MixinSwal;\n}\n\n/**\n * If `timer` parameter is set, returns number of milliseconds of timer remained.\n * Otherwise, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst getTimerLeft = () => {\n  return globalState.timeout && globalState.timeout.getTimerLeft();\n};\n\n/**\n * Stop timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst stopTimer = () => {\n  if (globalState.timeout) {\n    stopTimerProgressBar();\n    return globalState.timeout.stop();\n  }\n};\n\n/**\n * Resume timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst resumeTimer = () => {\n  if (globalState.timeout) {\n    const remaining = globalState.timeout.start();\n    animateTimerProgressBar(remaining);\n    return remaining;\n  }\n};\n\n/**\n * Resume timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst toggleTimer = () => {\n  const timer = globalState.timeout;\n  return timer && (timer.running ? stopTimer() : resumeTimer());\n};\n\n/**\n * Increase timer. Returns number of milliseconds of an updated timer.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @param {number} ms\n * @returns {number | undefined}\n */\nconst increaseTimer = ms => {\n  if (globalState.timeout) {\n    const remaining = globalState.timeout.increase(ms);\n    animateTimerProgressBar(remaining, true);\n    return remaining;\n  }\n};\n\n/**\n * Check if timer is running. Returns true if timer is running\n * or false if timer is paused or stopped.\n * If `timer` parameter isn't set, returns undefined\n *\n * @returns {boolean}\n */\nconst isTimerRunning = () => {\n  return !!(globalState.timeout && globalState.timeout.isRunning());\n};\n\nlet bodyClickListenerAdded = false;\nconst clickHandlers = {};\n\n/**\n * @param {string} attr\n */\nfunction bindClickHandler(attr = 'data-swal-template') {\n  clickHandlers[attr] = this;\n  if (!bodyClickListenerAdded) {\n    document.body.addEventListener('click', bodyClickListener);\n    bodyClickListenerAdded = true;\n  }\n}\nconst bodyClickListener = event => {\n  for (let el = event.target; el && el !== document; el = el.parentNode) {\n    for (const attr in clickHandlers) {\n      const template = el.getAttribute(attr);\n      if (template) {\n        clickHandlers[attr].fire({\n          template\n        });\n        return;\n      }\n    }\n  }\n};\n\n// Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\nclass EventEmitter {\n  constructor() {\n    /** @type {Events} */\n    this.events = {};\n  }\n\n  /**\n   * @param {string} eventName\n   * @returns {EventHandlers}\n   */\n  _getHandlersByEventName(eventName) {\n    if (typeof this.events[eventName] === 'undefined') {\n      // not Set because we need to keep the FIFO order\n      // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n      this.events[eventName] = [];\n    }\n    return this.events[eventName];\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  on(eventName, eventHandler) {\n    const currentHandlers = this._getHandlersByEventName(eventName);\n    if (!currentHandlers.includes(eventHandler)) {\n      currentHandlers.push(eventHandler);\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  once(eventName, eventHandler) {\n    /**\n     * @param {Array} args\n     */\n    const onceFn = (...args) => {\n      this.removeListener(eventName, onceFn);\n      eventHandler.apply(this, args);\n    };\n    this.on(eventName, onceFn);\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {Array} args\n   */\n  emit(eventName, ...args) {\n    this._getHandlersByEventName(eventName).forEach(\n    /**\n     * @param {EventHandler} eventHandler\n     */\n    eventHandler => {\n      try {\n        eventHandler.apply(this, args);\n      } catch (error) {\n        console.error(error);\n      }\n    });\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  removeListener(eventName, eventHandler) {\n    const currentHandlers = this._getHandlersByEventName(eventName);\n    const index = currentHandlers.indexOf(eventHandler);\n    if (index > -1) {\n      currentHandlers.splice(index, 1);\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   */\n  removeAllListeners(eventName) {\n    if (this.events[eventName] !== undefined) {\n      // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n      this.events[eventName].length = 0;\n    }\n  }\n  reset() {\n    this.events = {};\n  }\n}\n\nglobalState.eventEmitter = new EventEmitter();\n\n/**\n * @param {string} eventName\n * @param {EventHandler} eventHandler\n */\nconst on = (eventName, eventHandler) => {\n  globalState.eventEmitter.on(eventName, eventHandler);\n};\n\n/**\n * @param {string} eventName\n * @param {EventHandler} eventHandler\n */\nconst once = (eventName, eventHandler) => {\n  globalState.eventEmitter.once(eventName, eventHandler);\n};\n\n/**\n * @param {string} [eventName]\n * @param {EventHandler} [eventHandler]\n */\nconst off = (eventName, eventHandler) => {\n  // Remove all handlers for all events\n  if (!eventName) {\n    globalState.eventEmitter.reset();\n    return;\n  }\n  if (eventHandler) {\n    // Remove a specific handler\n    globalState.eventEmitter.removeListener(eventName, eventHandler);\n  } else {\n    // Remove all handlers for a specific event\n    globalState.eventEmitter.removeAllListeners(eventName);\n  }\n};\n\nvar staticMethods = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  argsToParams: argsToParams,\n  bindClickHandler: bindClickHandler,\n  clickCancel: clickCancel,\n  clickConfirm: clickConfirm,\n  clickDeny: clickDeny,\n  enableLoading: showLoading,\n  fire: fire,\n  getActions: getActions,\n  getCancelButton: getCancelButton,\n  getCloseButton: getCloseButton,\n  getConfirmButton: getConfirmButton,\n  getContainer: getContainer,\n  getDenyButton: getDenyButton,\n  getFocusableElements: getFocusableElements,\n  getFooter: getFooter,\n  getHtmlContainer: getHtmlContainer,\n  getIcon: getIcon,\n  getIconContent: getIconContent,\n  getImage: getImage,\n  getInputLabel: getInputLabel,\n  getLoader: getLoader,\n  getPopup: getPopup,\n  getProgressSteps: getProgressSteps,\n  getTimerLeft: getTimerLeft,\n  getTimerProgressBar: getTimerProgressBar,\n  getTitle: getTitle,\n  getValidationMessage: getValidationMessage,\n  increaseTimer: increaseTimer,\n  isDeprecatedParameter: isDeprecatedParameter,\n  isLoading: isLoading,\n  isTimerRunning: isTimerRunning,\n  isUpdatableParameter: isUpdatableParameter,\n  isValidParameter: isValidParameter,\n  isVisible: isVisible,\n  mixin: mixin,\n  off: off,\n  on: on,\n  once: once,\n  resumeTimer: resumeTimer,\n  showLoading: showLoading,\n  stopTimer: stopTimer,\n  toggleTimer: toggleTimer\n});\n\nclass Timer {\n  /**\n   * @param {Function} callback\n   * @param {number} delay\n   */\n  constructor(callback, delay) {\n    this.callback = callback;\n    this.remaining = delay;\n    this.running = false;\n    this.start();\n  }\n\n  /**\n   * @returns {number}\n   */\n  start() {\n    if (!this.running) {\n      this.running = true;\n      this.started = new Date();\n      this.id = setTimeout(this.callback, this.remaining);\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {number}\n   */\n  stop() {\n    if (this.started && this.running) {\n      this.running = false;\n      clearTimeout(this.id);\n      this.remaining -= new Date().getTime() - this.started.getTime();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @param {number} n\n   * @returns {number}\n   */\n  increase(n) {\n    const running = this.running;\n    if (running) {\n      this.stop();\n    }\n    this.remaining += n;\n    if (running) {\n      this.start();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {number}\n   */\n  getTimerLeft() {\n    if (this.running) {\n      this.stop();\n      this.start();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {boolean}\n   */\n  isRunning() {\n    return this.running;\n  }\n}\n\nconst swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {SweetAlertOptions}\n */\nconst getTemplateParams = params => {\n  const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n  if (!template) {\n    return {};\n  }\n  /** @type {DocumentFragment} */\n  const templateContent = template.content;\n  showWarningsForElements(templateContent);\n  const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalParams = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n  swalParams.forEach(param => {\n    showWarningsForAttributes(param, ['name', 'value']);\n    const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n    const value = param.getAttribute('value');\n    if (!paramName || !value) {\n      return;\n    }\n    if (typeof defaultParams[paramName] === 'boolean') {\n      result[paramName] = value !== 'false';\n    } else if (typeof defaultParams[paramName] === 'object') {\n      result[paramName] = JSON.parse(value);\n    } else {\n      result[paramName] = value;\n    }\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalFunctionParams = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n  swalFunctions.forEach(param => {\n    const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n    const value = param.getAttribute('value');\n    if (!paramName || !value) {\n      return;\n    }\n    result[paramName] = new Function(`return ${value}`)();\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalButtons = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n  swalButtons.forEach(button => {\n    showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n    const type = button.getAttribute('type');\n    if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n      return;\n    }\n    result[`${type}ButtonText`] = button.innerHTML;\n    result[`show${capitalizeFirstLetter(type)}Button`] = true;\n    if (button.hasAttribute('color')) {\n      result[`${type}ButtonColor`] = button.getAttribute('color');\n    }\n    if (button.hasAttribute('aria-label')) {\n      result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n    }\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n */\nconst getSwalImage = templateContent => {\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const image = templateContent.querySelector('swal-image');\n  if (image) {\n    showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n    if (image.hasAttribute('src')) {\n      result.imageUrl = image.getAttribute('src') || undefined;\n    }\n    if (image.hasAttribute('width')) {\n      result.imageWidth = image.getAttribute('width') || undefined;\n    }\n    if (image.hasAttribute('height')) {\n      result.imageHeight = image.getAttribute('height') || undefined;\n    }\n    if (image.hasAttribute('alt')) {\n      result.imageAlt = image.getAttribute('alt') || undefined;\n    }\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalIcon = templateContent => {\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const icon = templateContent.querySelector('swal-icon');\n  if (icon) {\n    showWarningsForAttributes(icon, ['type', 'color']);\n    if (icon.hasAttribute('type')) {\n      result.icon = icon.getAttribute('type');\n    }\n    if (icon.hasAttribute('color')) {\n      result.iconColor = icon.getAttribute('color');\n    }\n    result.iconHtml = icon.innerHTML;\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalInput = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const input = templateContent.querySelector('swal-input');\n  if (input) {\n    showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n    result.input = input.getAttribute('type') || 'text';\n    if (input.hasAttribute('label')) {\n      result.inputLabel = input.getAttribute('label');\n    }\n    if (input.hasAttribute('placeholder')) {\n      result.inputPlaceholder = input.getAttribute('placeholder');\n    }\n    if (input.hasAttribute('value')) {\n      result.inputValue = input.getAttribute('value');\n    }\n  }\n  /** @type {HTMLElement[]} */\n  const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n  if (inputOptions.length) {\n    result.inputOptions = {};\n    inputOptions.forEach(option => {\n      showWarningsForAttributes(option, ['value']);\n      const optionValue = option.getAttribute('value');\n      if (!optionValue) {\n        return;\n      }\n      const optionName = option.innerHTML;\n      result.inputOptions[optionValue] = optionName;\n    });\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @param {string[]} paramNames\n * @returns {Record<string, any>}\n */\nconst getSwalStringParams = (templateContent, paramNames) => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  for (const i in paramNames) {\n    const paramName = paramNames[i];\n    /** @type {HTMLElement | null} */\n    const tag = templateContent.querySelector(paramName);\n    if (tag) {\n      showWarningsForAttributes(tag, []);\n      result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n    }\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n */\nconst showWarningsForElements = templateContent => {\n  const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n  Array.from(templateContent.children).forEach(el => {\n    const tagName = el.tagName.toLowerCase();\n    if (!allowedElements.includes(tagName)) {\n      warn(`Unrecognized element <${tagName}>`);\n    }\n  });\n};\n\n/**\n * @param {HTMLElement} el\n * @param {string[]} allowedAttributes\n */\nconst showWarningsForAttributes = (el, allowedAttributes) => {\n  Array.from(el.attributes).forEach(attribute => {\n    if (allowedAttributes.indexOf(attribute.name) === -1) {\n      warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n    }\n  });\n};\n\nconst SHOW_CLASS_TIMEOUT = 10;\n\n/**\n * Open popup, add necessary classes and styles, fix scrollbar\n *\n * @param {SweetAlertOptions} params\n */\nconst openPopup = params => {\n  const container = getContainer();\n  const popup = getPopup();\n  if (typeof params.willOpen === 'function') {\n    params.willOpen(popup);\n  }\n  globalState.eventEmitter.emit('willOpen', popup);\n  const bodyStyles = window.getComputedStyle(document.body);\n  const initialBodyOverflow = bodyStyles.overflowY;\n  addClasses(container, popup, params);\n\n  // scrolling is 'hidden' until animation is done, after that 'auto'\n  setTimeout(() => {\n    setScrollingVisibility(container, popup);\n  }, SHOW_CLASS_TIMEOUT);\n  if (isModal()) {\n    fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n    setAriaHidden();\n  }\n  if (!isToast() && !globalState.previousActiveElement) {\n    globalState.previousActiveElement = document.activeElement;\n  }\n  if (typeof params.didOpen === 'function') {\n    setTimeout(() => params.didOpen(popup));\n  }\n  globalState.eventEmitter.emit('didOpen', popup);\n  removeClass(container, swalClasses['no-transition']);\n};\n\n/**\n * @param {AnimationEvent} event\n */\nconst swalOpenAnimationFinished = event => {\n  const popup = getPopup();\n  if (event.target !== popup) {\n    return;\n  }\n  const container = getContainer();\n  popup.removeEventListener('animationend', swalOpenAnimationFinished);\n  popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n  container.style.overflowY = 'auto';\n};\n\n/**\n * @param {HTMLElement} container\n * @param {HTMLElement} popup\n */\nconst setScrollingVisibility = (container, popup) => {\n  if (hasCssAnimation(popup)) {\n    container.style.overflowY = 'hidden';\n    popup.addEventListener('animationend', swalOpenAnimationFinished);\n    popup.addEventListener('transitionend', swalOpenAnimationFinished);\n  } else {\n    container.style.overflowY = 'auto';\n  }\n};\n\n/**\n * @param {HTMLElement} container\n * @param {boolean} scrollbarPadding\n * @param {string} initialBodyOverflow\n */\nconst fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n  iOSfix();\n  if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n    replaceScrollbarWithPadding(initialBodyOverflow);\n  }\n\n  // sweetalert2/issues/1247\n  setTimeout(() => {\n    container.scrollTop = 0;\n  });\n};\n\n/**\n * @param {HTMLElement} container\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} params\n */\nconst addClasses = (container, popup, params) => {\n  addClass(container, params.showClass.backdrop);\n  if (params.animation) {\n    // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n    popup.style.setProperty('opacity', '0', 'important');\n    show(popup, 'grid');\n    setTimeout(() => {\n      // Animate popup right after showing it\n      addClass(popup, params.showClass.popup);\n      // and remove the opacity workaround\n      popup.style.removeProperty('opacity');\n    }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n  } else {\n    show(popup, 'grid');\n  }\n  addClass([document.documentElement, document.body], swalClasses.shown);\n  if (params.heightAuto && params.backdrop && !params.toast) {\n    addClass([document.documentElement, document.body], swalClasses['height-auto']);\n  }\n};\n\nvar defaultInputValidators = {\n  /**\n   * @param {string} string\n   * @param {string} [validationMessage]\n   * @returns {Promise<string | void>}\n   */\n  email: (string, validationMessage) => {\n    return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n  },\n  /**\n   * @param {string} string\n   * @param {string} [validationMessage]\n   * @returns {Promise<string | void>}\n   */\n  url: (string, validationMessage) => {\n    // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n    return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nfunction setDefaultInputValidators(params) {\n  // Use default `inputValidator` for supported input types if not provided\n  if (params.inputValidator) {\n    return;\n  }\n  if (params.input === 'email') {\n    params.inputValidator = defaultInputValidators['email'];\n  }\n  if (params.input === 'url') {\n    params.inputValidator = defaultInputValidators['url'];\n  }\n}\n\n/**\n * @param {SweetAlertOptions} params\n */\nfunction validateCustomTargetElement(params) {\n  // Determine if the custom target element is valid\n  if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n    warn('Target parameter is not valid, defaulting to \"body\"');\n    params.target = 'body';\n  }\n}\n\n/**\n * Set type, text and actions on popup\n *\n * @param {SweetAlertOptions} params\n */\nfunction setParameters(params) {\n  setDefaultInputValidators(params);\n\n  // showLoaderOnConfirm && preConfirm\n  if (params.showLoaderOnConfirm && !params.preConfirm) {\n    warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n  }\n  validateCustomTargetElement(params);\n\n  // Replace newlines with <br> in title\n  if (typeof params.title === 'string') {\n    params.title = params.title.split('\\n').join('<br />');\n  }\n  init(params);\n}\n\n/** @type {SweetAlert} */\nlet currentInstance;\nvar _promise = /*#__PURE__*/new WeakMap();\nclass SweetAlert {\n  /**\n   * @param {...any} args\n   * @this {SweetAlert}\n   */\n  constructor(...args) {\n    /**\n     * @type {Promise<SweetAlertResult>}\n     */\n    _classPrivateFieldInitSpec(this, _promise, void 0);\n    // Prevent run in Node env\n    if (typeof window === 'undefined') {\n      return;\n    }\n    currentInstance = this;\n\n    // @ts-ignore\n    const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n    /** @type {Readonly<SweetAlertOptions>} */\n    this.params = outerParams;\n\n    /** @type {boolean} */\n    this.isAwaitingPromise = false;\n    _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n  }\n  _main(userParams, mixinParams = {}) {\n    showWarningsForParams(Object.assign({}, mixinParams, userParams));\n    if (globalState.currentInstance) {\n      const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n      const {\n        isAwaitingPromise\n      } = globalState.currentInstance;\n      globalState.currentInstance._destroy();\n      if (!isAwaitingPromise) {\n        swalPromiseResolve({\n          isDismissed: true\n        });\n      }\n      if (isModal()) {\n        unsetAriaHidden();\n      }\n    }\n    globalState.currentInstance = currentInstance;\n    const innerParams = prepareParams(userParams, mixinParams);\n    setParameters(innerParams);\n    Object.freeze(innerParams);\n\n    // clear the previous timer\n    if (globalState.timeout) {\n      globalState.timeout.stop();\n      delete globalState.timeout;\n    }\n\n    // clear the restore focus timeout\n    clearTimeout(globalState.restoreFocusTimeout);\n    const domCache = populateDomCache(currentInstance);\n    render(currentInstance, innerParams);\n    privateProps.innerParams.set(currentInstance, innerParams);\n    return swalPromise(currentInstance, domCache, innerParams);\n  }\n\n  // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n  then(onFulfilled) {\n    return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n  }\n  finally(onFinally) {\n    return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n  }\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n * @returns {Promise}\n */\nconst swalPromise = (instance, domCache, innerParams) => {\n  return new Promise((resolve, reject) => {\n    // functions to handle all closings/dismissals\n    /**\n     * @param {DismissReason} dismiss\n     */\n    const dismissWith = dismiss => {\n      instance.close({\n        isDismissed: true,\n        dismiss\n      });\n    };\n    privateMethods.swalPromiseResolve.set(instance, resolve);\n    privateMethods.swalPromiseReject.set(instance, reject);\n    domCache.confirmButton.onclick = () => {\n      handleConfirmButtonClick(instance);\n    };\n    domCache.denyButton.onclick = () => {\n      handleDenyButtonClick(instance);\n    };\n    domCache.cancelButton.onclick = () => {\n      handleCancelButtonClick(instance, dismissWith);\n    };\n    domCache.closeButton.onclick = () => {\n      dismissWith(DismissReason.close);\n    };\n    handlePopupClick(innerParams, domCache, dismissWith);\n    addKeydownHandler(globalState, innerParams, dismissWith);\n    handleInputOptionsAndValue(instance, innerParams);\n    openPopup(innerParams);\n    setupTimer(globalState, innerParams, dismissWith);\n    initFocus(domCache, innerParams);\n\n    // Scroll container to top on open (#1247, #1946)\n    setTimeout(() => {\n      domCache.container.scrollTop = 0;\n    });\n  });\n};\n\n/**\n * @param {SweetAlertOptions} userParams\n * @param {SweetAlertOptions} mixinParams\n * @returns {SweetAlertOptions}\n */\nconst prepareParams = (userParams, mixinParams) => {\n  const templateParams = getTemplateParams(userParams);\n  const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n  params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n  params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n  if (params.animation === false) {\n    params.showClass = {\n      backdrop: 'swal2-noanimation'\n    };\n    params.hideClass = {};\n  }\n  return params;\n};\n\n/**\n * @param {SweetAlert} instance\n * @returns {DomCache}\n */\nconst populateDomCache = instance => {\n  const domCache = {\n    popup: getPopup(),\n    container: getContainer(),\n    actions: getActions(),\n    confirmButton: getConfirmButton(),\n    denyButton: getDenyButton(),\n    cancelButton: getCancelButton(),\n    loader: getLoader(),\n    closeButton: getCloseButton(),\n    validationMessage: getValidationMessage(),\n    progressSteps: getProgressSteps()\n  };\n  privateProps.domCache.set(instance, domCache);\n  return domCache;\n};\n\n/**\n * @param {GlobalState} globalState\n * @param {SweetAlertOptions} innerParams\n * @param {Function} dismissWith\n */\nconst setupTimer = (globalState, innerParams, dismissWith) => {\n  const timerProgressBar = getTimerProgressBar();\n  hide(timerProgressBar);\n  if (innerParams.timer) {\n    globalState.timeout = new Timer(() => {\n      dismissWith('timer');\n      delete globalState.timeout;\n    }, innerParams.timer);\n    if (innerParams.timerProgressBar) {\n      show(timerProgressBar);\n      applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n      setTimeout(() => {\n        if (globalState.timeout && globalState.timeout.running) {\n          // timer can be already stopped or unset at this point\n          animateTimerProgressBar(innerParams.timer);\n        }\n      });\n    }\n  }\n};\n\n/**\n * Initialize focus in the popup:\n *\n * 1. If `toast` is `true`, don't steal focus from the document.\n * 2. Else if there is an [autofocus] element, focus it.\n * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n * 6. Else focus the first focusable element in a popup (if any).\n *\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n */\nconst initFocus = (domCache, innerParams) => {\n  if (innerParams.toast) {\n    return;\n  }\n  // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n  if (!callIfFunction(innerParams.allowEnterKey)) {\n    warnAboutDeprecation('allowEnterKey');\n    blurActiveElement();\n    return;\n  }\n  if (focusAutofocus(domCache)) {\n    return;\n  }\n  if (focusButton(domCache, innerParams)) {\n    return;\n  }\n  setFocus(-1, 1);\n};\n\n/**\n * @param {DomCache} domCache\n * @returns {boolean}\n */\nconst focusAutofocus = domCache => {\n  const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n  for (const autofocusElement of autofocusElements) {\n    if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n      autofocusElement.focus();\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n * @returns {boolean}\n */\nconst focusButton = (domCache, innerParams) => {\n  if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n    domCache.denyButton.focus();\n    return true;\n  }\n  if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n    domCache.cancelButton.focus();\n    return true;\n  }\n  if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n    domCache.confirmButton.focus();\n    return true;\n  }\n  return false;\n};\nconst blurActiveElement = () => {\n  if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n    document.activeElement.blur();\n  }\n};\n\n// Dear russian users visiting russian sites. Let's have fun.\nif (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n  const now = new Date();\n  const initiationDate = localStorage.getItem('swal-initiation');\n  if (!initiationDate) {\n    localStorage.setItem('swal-initiation', `${now}`);\n  } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n    setTimeout(() => {\n      document.body.style.pointerEvents = 'none';\n      const ukrainianAnthem = document.createElement('audio');\n      ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n      ukrainianAnthem.loop = true;\n      document.body.appendChild(ukrainianAnthem);\n      setTimeout(() => {\n        ukrainianAnthem.play().catch(() => {\n          // ignore\n        });\n      }, 2500);\n    }, 500);\n  }\n}\n\n// Assign instance methods from src/instanceMethods/*.js to prototype\nSweetAlert.prototype.disableButtons = disableButtons;\nSweetAlert.prototype.enableButtons = enableButtons;\nSweetAlert.prototype.getInput = getInput;\nSweetAlert.prototype.disableInput = disableInput;\nSweetAlert.prototype.enableInput = enableInput;\nSweetAlert.prototype.hideLoading = hideLoading;\nSweetAlert.prototype.disableLoading = hideLoading;\nSweetAlert.prototype.showValidationMessage = showValidationMessage;\nSweetAlert.prototype.resetValidationMessage = resetValidationMessage;\nSweetAlert.prototype.close = close;\nSweetAlert.prototype.closePopup = close;\nSweetAlert.prototype.closeModal = close;\nSweetAlert.prototype.closeToast = close;\nSweetAlert.prototype.rejectPromise = rejectPromise;\nSweetAlert.prototype.update = update;\nSweetAlert.prototype._destroy = _destroy;\n\n// Assign static methods from src/staticMethods/*.js to constructor\nObject.assign(SweetAlert, staticMethods);\n\n// Proxy to instance methods to constructor, for now, for backwards compatibility\nObject.keys(instanceMethods).forEach(key => {\n  /**\n   * @param {...any} args\n   * @returns {any | undefined}\n   */\n  SweetAlert[key] = function (...args) {\n    if (currentInstance && currentInstance[key]) {\n      return currentInstance[key](...args);\n    }\n    return null;\n  };\n});\nSweetAlert.DismissReason = DismissReason;\nSweetAlert.version = '11.22.2';\n\nconst Swal = SweetAlert;\n// @ts-ignore\nSwal.default = Swal;\n\n\n\"undefined\"!=typeof document&&function(e,t){var n=e.createElement(\"style\");if(e.getElementsByTagName(\"head\")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,\":root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:\\\"top-start     top            top-end\\\" \\\"center-start  center         center-end\\\" \\\"bottom-start  bottom-center  bottom-end\\\";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:\\\"!\\\";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\n");

/***/ })

};
;