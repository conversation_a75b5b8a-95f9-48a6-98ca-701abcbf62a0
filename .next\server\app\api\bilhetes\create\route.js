"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bilhetes/create/route";
exports.ids = ["app/api/bilhetes/create/route"];
exports.modules = {

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbilhetes%2Fcreate%2Froute&page=%2Fapi%2Fbilhetes%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbilhetes%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbilhetes%2Fcreate%2Froute&page=%2Fapi%2Fbilhetes%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbilhetes%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_bilhetes_create_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/bilhetes/create/route.ts */ \"(rsc)/./app/api/bilhetes/create/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bilhetes/create/route\",\n        pathname: \"/api/bilhetes/create\",\n        filename: \"route\",\n        bundlePath: \"app/api/bilhetes/create/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\bilhetes\\\\create\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_bilhetes_create_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/bilhetes/create/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbilhetes%2Fcreate%2Froute&page=%2Fapi%2Fbilhetes%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbilhetes%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/bilhetes/create/route.ts":
/*!******************************************!*\
  !*** ./app/api/bilhetes/create/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(rsc)/./lib/database-config.js\");\n\n\nasync function POST(request) {\n    try {\n        await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.initializeDatabase)();\n        const body = await request.json();\n        const { user_id, apostas, valor, qr_code_pix, transaction_id, pix_order_id, client_name, client_email, client_document, cambista_id } = body;\n        console.log(\"\\uD83C\\uDFAB Iniciando cria\\xe7\\xe3o de bilhete:\", {\n            user_id,\n            valor,\n            apostas: apostas?.length,\n            client_name,\n            client_email,\n            client_document,\n            cambista_id,\n            transaction_id,\n            pix_order_id\n        });\n        // Validações aprimoradas\n        if (!user_id) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: user_id obrigat\\xf3rio\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Usu\\xe1rio deve estar logado para criar bilhetes\"\n            }, {\n                status: 400\n            });\n        }\n        // Verificar se o usuário existe no banco de dados\n        console.log(\"\\uD83D\\uDD0D Verificando se usu\\xe1rio existe no banco:\", user_id);\n        const usuarioExiste = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(\"SELECT id FROM usuarios WHERE id = ?\", [\n            user_id\n        ]);\n        console.log(\"\\uD83D\\uDCCA Resultado da verifica\\xe7\\xe3o de usu\\xe1rio:\", usuarioExiste);\n        if (!usuarioExiste || usuarioExiste.length === 0) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: user_id n\\xe3o existe no banco:\", user_id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Usu\\xe1rio n\\xe3o encontrado no sistema\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"✅ Usu\\xe1rio validado com sucesso:\", usuarioExiste[0]);\n        if (!apostas || !Array.isArray(apostas) || apostas.length === 0) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: apostas inv\\xe1lidas\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Pelo menos uma aposta \\xe9 obrigat\\xf3ria\"\n            }, {\n                status: 400\n            });\n        }\n        if (!valor || valor <= 0) {\n            console.log(\"❌ Valida\\xe7\\xe3o falhou: valor inv\\xe1lido\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Valor deve ser maior que zero\"\n            }, {\n                status: 400\n            });\n        }\n        // Validação dos dados do cliente\n        // Aplicar valores padrão se os dados estiverem vazios\n        const finalClientName = client_name && client_name.trim() !== \"\" ? client_name.trim() : \"Usu\\xe1rio Padr\\xe3o\";\n        const finalClientEmail = client_email && client_email.trim() !== \"\" ? client_email.trim() : \"<EMAIL>\";\n        const finalClientDocument = client_document && client_document.trim() !== \"\" ? client_document.trim() : \"00000000000\";\n        // Gerar código único do bilhete mais robusto\n        const timestamp = Date.now();\n        const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();\n        const codigoBilhete = `BLT${timestamp}${user_id}${randomSuffix}`;\n        console.log(\"\\uD83D\\uDD22 C\\xf3digo do bilhete gerado:\", codigoBilhete);\n        // Inserir bilhete com tratamento de erro específico\n        try {\n            console.log(\"\\uD83D\\uDCCA Par\\xe2metros para inser\\xe7\\xe3o do bilhete:\", {\n                codigoBilhete,\n                user_id,\n                cambista_id: cambista_id || null,\n                finalClientName,\n                finalClientEmail,\n                finalClientDocument,\n                valor,\n                quantidade_apostas: apostas.length,\n                qr_code_pix: qr_code_pix || null,\n                transaction_id: transaction_id || null,\n                pix_order_id: pix_order_id || null\n            });\n            const bilheteResult = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n        INSERT INTO bilhetes (\n          codigo, usuario_id, cambista_id, usuario_nome, usuario_email, usuario_cpf,\n          valor_total, quantidade_apostas, status, qr_code_pix, transaction_id, pix_order_id\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pendente', ?, ?, ?)\n      `, [\n                codigoBilhete,\n                user_id,\n                cambista_id || null,\n                finalClientName,\n                finalClientEmail,\n                finalClientDocument,\n                valor,\n                apostas.length,\n                qr_code_pix || null,\n                transaction_id || null,\n                pix_order_id || null\n            ]);\n            const bilheteId = bilheteResult.insertId;\n            console.log(\"✅ Bilhete inserido no banco com ID:\", bilheteId);\n            // Inserir apostas individuais se necessário (futuro)\n            console.log(\"\\uD83D\\uDCCA Apostas do bilhete recebidas:\", apostas);\n            console.log(\"\\uD83D\\uDCCA Apostas formatadas:\", apostas.map((a)=>({\n                    jogo_id: a.jogo_id,\n                    palpite: a.palpite,\n                    jogo_nome: a.jogo_nome,\n                    odd: a.odd\n                })));\n            const bilheteCompleto = {\n                id: bilheteId,\n                codigo: codigoBilhete,\n                valor,\n                status: \"pendente\",\n                qr_code_pix,\n                transaction_id,\n                apostas_count: apostas.length,\n                finalClientName,\n                finalClientEmail,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"\\uD83C\\uDF89 Bilhete criado com sucesso:\", bilheteCompleto);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"Bilhete criado com sucesso\",\n                bilhete: bilheteCompleto\n            });\n        } catch (dbError) {\n            console.error(\"❌ Erro espec\\xedfico do banco de dados:\", dbError);\n            // Verificar se é erro de coluna inexistente\n            if (dbError instanceof Error && dbError.message.includes(\"Unknown column\")) {\n                console.log(\"\\uD83D\\uDD04 Tentando INSERT simplificado...\");\n                const bilheteResultSimple = await (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.executeQuery)(`\n          INSERT INTO bilhetes (codigo, usuario_id, valor_total, status, qr_code_pix, transaction_id, pix_order_id)\n          VALUES (?, ?, ?, 'pendente', ?, ?, ?)\n        `, [\n                    codigoBilhete,\n                    user_id,\n                    valor,\n                    qr_code_pix || null,\n                    transaction_id || null,\n                    pix_order_id || null\n                ]);\n                const bilheteId = bilheteResultSimple.insertId;\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: \"Bilhete criado com sucesso (modo simplificado)\",\n                    bilhete: {\n                        id: bilheteId,\n                        codigo: codigoBilhete,\n                        valor,\n                        status: \"pendente\",\n                        qr_code_pix,\n                        transaction_id,\n                        apostas_count: apostas.length\n                    }\n                });\n            }\n            throw dbError;\n        }\n    } catch (error) {\n        console.error(\"❌ Erro geral ao criar bilhete:\", error);\n        console.error(\"❌ Stack trace completo:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erro interno do servidor\",\n            message: \"N\\xe3o foi poss\\xedvel criar o bilhete\",\n            details: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/bilhetes/create/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-config.js":
/*!********************************!*\
  !*** ./lib/database-config.js ***!
  \********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeQuerySingle: () => (/* binding */ executeQuerySingle),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n// Configuração para usar APENAS MySQL\n// Conforme solicitado pelo usuário - NUNCA usar SQLite\n\nlet pool = null;\n// Configuração do banco de dados MySQL otimizada para evitar \"Too many connections\"\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"\",\n    database: process.env.DB_NAME || \"sistema-bolao-top\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool de conexões otimizado\n    connectionLimit: 5,\n    maxIdle: 2,\n    idleTimeout: 30000,\n    queueLimit: 50,\n    waitForConnections: true\n};\n// Função para inicializar o pool de conexões MySQL\nasync function initializeDatabase() {\n    try {\n        if (!pool) {\n            console.log(\"\\uD83D\\uDD27 Inicializando pool MySQL para sistema-bolao-top...\");\n            pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n            console.log(\"✅ Pool de conex\\xf5es MySQL inicializado com sucesso\");\n        }\n        // Testar a conexão\n        const connection = await pool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ Conex\\xe3o com MySQL sistema-bolao-top estabelecida com sucesso\");\n        return pool;\n    } catch (error) {\n        console.error(\"❌ Erro ao inicializar banco de dados MySQL:\", error);\n        throw error;\n    }\n}\n// Função para obter o pool de conexões\nasync function getDatabase() {\n    if (!pool) {\n        await initializeDatabase();\n    }\n    return pool;\n}\n// Função para executar queries com retry automático\nasync function executeQuery(query, params = [], retries = 3) {\n    let connection = null;\n    let lastError = null;\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const pool = await getDatabase();\n            // Usar timeout para adquirir conexão\n            connection = await Promise.race([\n                pool.getConnection(),\n                new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout ao adquirir conex\\xe3o do pool\")), 30000))\n            ]);\n            const [results] = await connection.execute(query, params);\n            return results;\n        } catch (error) {\n            lastError = error;\n            console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message);\n            // Se é erro de muitas conexões, aguardar e tentar novamente\n            if (error.code === \"ER_CON_COUNT_ERROR\" || error.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || error.message?.includes(\"Too many connections\") || error.message?.includes(\"Timeout ao adquirir conex\\xe3o\")) {\n                if (attempt < retries) {\n                    const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s\n                    ;\n                    console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`);\n                    await new Promise((resolve)=>setTimeout(resolve, waitTime));\n                    continue;\n                }\n            }\n            // Para outros erros de conexão, tentar novamente\n            if (error.code === \"ECONNREFUSED\" || error.code === \"PROTOCOL_CONNECTION_LOST\") {\n                if (attempt < retries) {\n                    console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`);\n                    await new Promise((resolve)=>setTimeout(resolve, attempt * 1000));\n                    continue;\n                }\n            }\n            break;\n        } finally{\n            if (connection) {\n                try {\n                    connection.release();\n                } catch (releaseError) {\n                    console.warn(\"⚠️ Erro ao liberar conex\\xe3o:\", releaseError.message);\n                }\n                connection = null;\n            }\n        }\n    }\n    // Se chegou aqui, todas as tentativas falharam\n    console.error(\"❌ Falha ap\\xf3s todas as tentativas:\", lastError?.message || lastError);\n    // Para erro de \"Too many connections\", retornar dados básicos em vez de falhar\n    if (lastError?.code === \"ER_CON_COUNT_ERROR\" || lastError?.code === \"ER_TOO_MANY_USER_CONNECTIONS\" || lastError?.message?.includes(\"Too many connections\")) {\n        console.warn(\"⚠️ Erro ao acessar banco, retornando dados b\\xe1sicos:\", lastError.message);\n        return [] // Retorna array vazio em vez de falhar\n        ;\n    }\n    throw lastError;\n}\n// Função para executar query única\nasync function executeQuerySingle(query, params = []) {\n    const results = await executeQuery(query, params);\n    return Array.isArray(results) && results.length > 0 ? results[0] : null;\n}\n// Função para fechar conexões\nasync function closeDatabase() {\n    if (pool) {\n        await pool.end();\n        pool = null;\n        console.log(\"✅ Pool de conex\\xf5es MySQL fechado\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-config.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbilhetes%2Fcreate%2Froute&page=%2Fapi%2Fbilhetes%2Fcreate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbilhetes%2Fcreate%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();