"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/pix/qrcode/route";
exports.ids = ["app/api/pix/qrcode/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fqrcode%2Froute&page=%2Fapi%2Fpix%2Fqrcode%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fqrcode%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fqrcode%2Froute&page=%2Fapi%2Fpix%2Fqrcode%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fqrcode%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_pix_qrcode_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/pix/qrcode/route.ts */ \"(rsc)/./app/api/pix/qrcode/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/pix/qrcode/route\",\n        pathname: \"/api/pix/qrcode\",\n        filename: \"route\",\n        bundlePath: \"app/api/pix/qrcode/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\xamp8.1\\\\htdocs\\\\app\\\\api\\\\pix\\\\qrcode\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_yakus_Desktop_xamp8_1_htdocs_app_api_pix_qrcode_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/pix/qrcode/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fqrcode%2Froute&page=%2Fapi%2Fpix%2Fqrcode%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fqrcode%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/pix/qrcode/route.ts":
/*!*************************************!*\
  !*** ./app/api/pix/qrcode/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! qrcode */ \"(rsc)/./node_modules/qrcode/lib/index.js\");\n\n\nconst PIX_API_BASE_URL = process.env.PIX_API_URL || \"https://ouroemu.site/api/v1\";\nconst PIX_TOKEN = process.env.PIX_API_TOKEN || \"Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w==\";\nconst PIX_WEBHOOK_URL = process.env.PIX_WEBHOOK_URL || \"https://seudominio.com/api/v1/MP/webhookruntransation\";\nasync function POST(request) {\n    try {\n        let body;\n        try {\n            body = await request.json();\n        } catch (parseError) {\n            console.error(\"❌ Erro ao parsear JSON da requisi\\xe7\\xe3o:\", parseError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"JSON inv\\xe1lido na requisi\\xe7\\xe3o\"\n            }, {\n                status: 400\n            });\n        }\n        const { value, description = \"Pagamento de aposta\", client_name, client_email, client_document, qrcode_image = false } = body;\n        // Validações\n        if (!value || value <= 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Valor deve ser maior que zero\"\n            }, {\n                status: 400\n            });\n        }\n        if (!client_name || !client_email || !client_document) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Nome, email e documento do cliente s\\xe3o obrigat\\xf3rios\"\n            }, {\n                status: 400\n            });\n        }\n        // Limpar formatação do CPF (remover pontos e traços)\n        const cleanCpf = client_document.replace(/[.-]/g, \"\");\n        console.log(\"\\uD83E\\uDDF9 CPF limpo:\", {\n            original: client_document,\n            clean: cleanCpf\n        });\n        // Preparar dados para a API do PIX\n        const pixData = {\n            token: PIX_TOKEN,\n            value: parseFloat(value.toString()),\n            description,\n            client_name,\n            client_email,\n            client_document: cleanCpf,\n            qrcode_image,\n            webhook_url: PIX_WEBHOOK_URL // URL do webhook para notificações\n        };\n        console.log(\"\\uD83D\\uDD04 Solicitando QR Code PIX:\", {\n            value: pixData.value,\n            client_name: pixData.client_name,\n            client_email: pixData.client_email\n        });\n        // Fazer requisição para a API do PIX com timeout personalizado\n        console.log(\"\\uD83D\\uDD04 Fazendo requisi\\xe7\\xe3o para API PIX real...\");\n        let pixResponse;\n        try {\n            // Criar AbortController para timeout personalizado\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15000) // 15 segundos\n            ;\n            const response = await fetch(`${PIX_API_BASE_URL}/Transacao/SolicitacaoQRCode`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(pixData),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"❌ Erro na API PIX:\", response.status, errorText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Erro na API PIX: ${response.status}`,\n                    details: errorText,\n                    message: \"N\\xe3o foi poss\\xedvel gerar o QR Code PIX. Tente novamente.\"\n                }, {\n                    status: response.status\n                });\n            }\n            const responseText = await response.text();\n            console.log(\"\\uD83D\\uDCE5 Resposta bruta da API PIX:\", responseText.substring(0, 200) + \"...\");\n            try {\n                pixResponse = JSON.parse(responseText);\n            } catch (parseError) {\n                console.error(\"❌ Erro ao parsear resposta da API PIX:\", parseError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Resposta inv\\xe1lida da API PIX\",\n                    details: responseText.substring(0, 500),\n                    message: \"Erro interno na API PIX. Contate o suporte.\"\n                }, {\n                    status: 500\n                });\n            }\n            // Verificar se a resposta contém os dados necessários\n            if (!pixResponse || !pixResponse.qr_code_value || !pixResponse.transaction_id) {\n                console.error(\"❌ Resposta da API PIX incompleta:\", pixResponse);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Dados incompletos da API PIX\",\n                    details: pixResponse,\n                    message: \"A API PIX n\\xe3o retornou os dados necess\\xe1rios.\"\n                }, {\n                    status: 500\n                });\n            }\n            console.log(\"✅ QR Code PIX gerado pela API real:\", {\n                transaction_id: pixResponse.transaction_id,\n                order_id: pixResponse.order_id,\n                status: pixResponse.status,\n                has_qr_code: !!pixResponse.qr_code_value,\n                has_image: !!pixResponse.qrcode_image,\n                qr_code_length: pixResponse.qr_code_value?.length\n            });\n        } catch (fetchError) {\n            console.error(\"❌ Erro de conex\\xe3o com API PIX:\", fetchError);\n            // Se for ambiente de desenvolvimento, usar fallback\n            if (true) {\n                console.log(\"\\uD83D\\uDD04 Usando fallback para desenvolvimento...\");\n                // Gerar dados simulados para desenvolvimento\n                const mockTransactionId = `DEV_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n                const mockPixCode = `00020126580014br.gov.bcb.pix0136${mockTransactionId}520400005303986540${value.toFixed(2)}5802BR5913${client_name.substring(0, 25)}6009SAO PAULO62070503***6304`;\n                pixResponse = {\n                    success: true,\n                    transaction_id: mockTransactionId,\n                    order_id: `ORDER_${mockTransactionId}`,\n                    qr_code_value: mockPixCode,\n                    status: \"pending\",\n                    expiration_datetime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),\n                    qrcode_image: null\n                };\n                console.log(\"✅ Dados PIX simulados gerados para desenvolvimento:\", {\n                    transaction_id: pixResponse.transaction_id,\n                    has_qr_code: !!pixResponse.qr_code_value\n                });\n            } else {}\n        }\n        // Verificar se pixResponse foi definido\n        if (!pixResponse) {\n            console.error(\"❌ pixResponse n\\xe3o foi definido\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Erro interno\",\n                message: \"Falha na comunica\\xe7\\xe3o com a API PIX.\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"✅ QR Code PIX gerado com sucesso:\", {\n            transaction_id: pixResponse.transaction_id,\n            order_id: pixResponse.order_id,\n            status: pixResponse.status\n        });\n        // Validar e corrigir formato do QR Code se necessário\n        if (pixResponse.qr_code_value) {\n            console.log(\"\\uD83D\\uDD0D Validando formato PIX:\", {\n                length: pixResponse.qr_code_value.length,\n                startsCorrect: pixResponse.qr_code_value.startsWith(\"00020126\"),\n                containsPix: pixResponse.qr_code_value.includes(\"br.gov.bcb.pix\"),\n                containsCurrency: pixResponse.qr_code_value.includes(\"5303986\"),\n                containsCountry: pixResponse.qr_code_value.includes(\"5802BR\")\n            });\n        }\n        // Gerar QR Code visual se não veio da API\n        let qrCodeImage = pixResponse.qrcode_image;\n        if (!qrCodeImage && pixResponse.qr_code_value) {\n            try {\n                qrCodeImage = await qrcode__WEBPACK_IMPORTED_MODULE_1__.toDataURL(pixResponse.qr_code_value, {\n                    width: 300,\n                    margin: 2,\n                    color: {\n                        dark: \"#000000\",\n                        light: \"#FFFFFF\"\n                    },\n                    errorCorrectionLevel: \"M\"\n                });\n                console.log(\"✅ QR Code visual gerado localmente\");\n            } catch (qrError) {\n                console.error(\"❌ Erro ao gerar QR Code visual:\", qrError);\n            }\n        }\n        // Retornar resposta formatada\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                qr_code_value: pixResponse.qr_code_value,\n                qrcode_image: qrCodeImage,\n                expiration_datetime: pixResponse.expiration_datetime,\n                status: pixResponse.status,\n                transaction_id: pixResponse.transaction_id,\n                order_id: pixResponse.order_id,\n                value: pixData.value,\n                client_name: pixData.client_name,\n                client_email: pixData.client_email\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Erro interno ao gerar QR Code PIX:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erro interno do servidor\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/pix/qrcode/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/qrcode","vendor-chunks/pngjs","vendor-chunks/dijkstrajs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpix%2Fqrcode%2Froute&page=%2Fapi%2Fpix%2Fqrcode%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpix%2Fqrcode%2Froute.ts&appDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cyakus%5CDesktop%5Cxamp8.1%5Chtdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();